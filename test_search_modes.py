#!/usr/bin/env python3
"""
測試搜索模式執行問題
"""

import sys
import os
import logging
import traceback

# 設置日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_search_mode(mode_num, query_text="test query"):
    """測試特定搜索模式"""
    print(f"\n{'='*60}")
    print(f"測試搜索模式 {mode_num}")
    print('='*60)
    
    try:
        from src.ui import TongRAGInterface
        from src.search_engine import ChromaSearchEngine
        import io
        
        # 初始化
        print("初始化搜索引擎...")
        search_engine = ChromaSearchEngine()
        
        print("創建界面...")
        interface = TongRAGInterface(search_engine)
        
        # 模擬執行 execute_search
        print(f"\n執行搜索模式 {mode_num}...")
        
        # 保存原始 stdin
        original_stdin = sys.stdin
        
        # 根據不同模式準備輸入
        if mode_num == '1':  # 語義搜索
            print("模式: 語義搜索")
            sys.stdin = io.StringIO(f"{query_text}\n")
            interface.execute_search('1')
            
        elif mode_num == '2':  # 關鍵詞搜索
            print("模式: 關鍵詞搜索")
            sys.stdin = io.StringIO(f"{query_text}\n")
            interface.execute_search('2')
            
        elif mode_num == '3':  # 正則表達式搜索
            print("模式: 正則表達式搜索")
            sys.stdin = io.StringIO("test.*\n")
            interface.execute_search('3')
            
        elif mode_num == '4':  # 混合搜索
            print("模式: 混合搜索")
            sys.stdin = io.StringIO(f"{query_text}\nkeyword\n")
            interface.execute_search('4')
            
        elif mode_num == '5':  # 元數據篩選搜索
            print("模式: 元數據篩選搜索")
            sys.stdin = io.StringIO(f"{query_text}\n\n")  # 空的篩選條件
            interface.execute_search('5')
            
        elif mode_num == '6':  # 語義搜索（含重排序）
            print("模式: 語義搜索（含重排序）")
            sys.stdin = io.StringIO(f"{query_text}\n")
            interface.execute_search('6')
        
        # 恢復 stdin
        sys.stdin = original_stdin
        
        print(f"✓ 搜索模式 {mode_num} 執行成功")
        return True
        
    except Exception as e:
        print(f"✗ 搜索模式 {mode_num} 執行失敗: {e}")
        logger.error(f"Mode {mode_num} failed", exc_info=True)
        # 確保恢復 stdin
        sys.stdin = sys.__stdin__
        return False

def test_interactive_session():
    """測試完整的交互會話"""
    print(f"\n{'='*60}")
    print("測試完整交互會話")
    print('='*60)
    
    try:
        from src.ui import TongRAGInterface
        from src.search_engine import ChromaSearchEngine
        import io
        
        # 初始化
        print("初始化系統...")
        search_engine = ChromaSearchEngine()
        interface = TongRAGInterface(search_engine)
        
        # 準備輸入序列
        # 選擇模式1 -> 輸入查詢 -> 返回菜單(r) -> 選擇模式0退出
        input_sequence = "1\ntest query\nr\n0\n"
        
        print("\n模擬輸入序列:")
        print("  1. 選擇模式 1 (語義搜索)")
        print("  2. 輸入查詢 'test query'")
        print("  3. 返回菜單 (r)")
        print("  4. 退出 (0)")
        
        # 設置模擬輸入
        sys.stdin = io.StringIO(input_sequence)
        
        # 限制執行時間
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("執行超時")
        
        # 設置 10 秒超時
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)
        
        try:
            # 執行交互會話
            interface.run_interactive_session()
            print("✓ 交互會話正常結束")
            result = True
        except TimeoutError:
            print("✗ 交互會話超時")
            result = False
        finally:
            # 取消超時
            signal.alarm(0)
            # 恢復 stdin
            sys.stdin = sys.__stdin__
        
        return result
        
    except Exception as e:
        print(f"✗ 交互會話測試失敗: {e}")
        logger.error("Interactive session failed", exc_info=True)
        sys.stdin = sys.__stdin__
        return False

def test_input_handling():
    """測試輸入處理邏輯"""
    print(f"\n{'='*60}")
    print("測試輸入處理邏輯")
    print('='*60)
    
    try:
        from src.ui.ui_menu import MenuUI
        import io
        
        # 創建假的搜索引擎
        class MockSearchEngine:
            def semantic_search(self, query, n_results):
                return [
                    {"text": "測試結果1", "distance": 0.1, "metadata": {}},
                    {"text": "測試結果2", "distance": 0.2, "metadata": {}}
                ]
            
            def keyword_search(self, query, n_results):
                return []
        
        mock_engine = MockSearchEngine()
        menu_ui = MenuUI(mock_engine)
        
        # 測試各種輸入情況
        test_cases = [
            ("正常輸入", "test query", False),
            ("空輸入", "", True),  # 應該被驗證拒絕
            ("特殊命令 quit", "quit", False),  # 應該返回 'QUIT'
            ("特殊命令 help", "help\ntest", False),  # 應該顯示幫助然後繼續
        ]
        
        original_stdin = sys.stdin
        
        for test_name, test_input, should_fail in test_cases:
            print(f"\n測試: {test_name}")
            print(f"  輸入: '{test_input}'")
            
            try:
                sys.stdin = io.StringIO(test_input + "\n")
                result = menu_ui.get_user_query("測試輸入: ")
                
                if should_fail:
                    print(f"  ✗ 應該失敗但返回了: '{result}'")
                else:
                    print(f"  ✓ 返回: '{result}'")
                    
            except Exception as e:
                if should_fail:
                    print(f"  ✓ 預期的失敗: {e}")
                else:
                    print(f"  ✗ 意外的錯誤: {e}")
        
        sys.stdin = original_stdin
        return True
        
    except Exception as e:
        print(f"✗ 輸入處理測試失敗: {e}")
        logger.error("Input handling test failed", exc_info=True)
        sys.stdin = sys.__stdin__
        return False

def main():
    """主測試函數"""
    print("="*60)
    print("TongRAG3 搜索模式測試")
    print("="*60)
    
    # 測試單個搜索模式
    modes_to_test = ['1', '2', '3', '4', '5', '6']
    
    print("\n開始測試各個搜索模式...")
    for mode in modes_to_test:
        if not test_search_mode(mode):
            print(f"\n⚠️  模式 {mode} 測試失敗，繼續其他測試...")
    
    # 測試輸入處理
    test_input_handling()
    
    # 測試完整會話（可能會超時）
    print("\n測試完整交互會話（可能需要較長時間）...")
    # test_interactive_session()  # 暫時註釋，因為可能會掛起
    
    print("\n"+"="*60)
    print("測試完成")
    print("="*60)

if __name__ == "__main__":
    main()