"""
文檔重疊區域檢測器

本模組提供文檔塊間重疊區域檢測功能，用於識別和分析搜索結果中
的重複內容，幫助用戶快速識別新資訊。

主要功能：
- 檢測文檔塊間的文本重疊
- 計算重疊區域的精確範圍  
- 區分重疊區域和獨有區域
- 提供重疊統計信息
"""

import re
from typing import Dict, List, Tuple, Optional, Set
from difflib import SequenceMatcher


class OverlapDetector:
    """文檔重疊區域檢測器"""
    
    def __init__(self, min_overlap_length: int = 50, similarity_threshold: float = 0.8):
        """
        初始化重疊檢測器
        
        Args:
            min_overlap_length: 最小重疊長度（字符數）
            similarity_threshold: 相似度閾值（0-1之間）
        """
        self.min_overlap_length = min_overlap_length
        self.similarity_threshold = similarity_threshold
    
    def detect_overlaps(self, results: List[Dict]) -> List[Dict]:
        """
        檢測搜索結果中的重疊區域
        
        Args:
            results: 搜索結果列表
            
        Returns:
            增強的結果列表，包含重疊檢測信息
        """
        if len(results) < 2:
            print(f"[DEBUG-DETAIL] 結果數量少於2個 ({len(results)})，跳過重疊檢測")
            return self._mark_no_overlap(results)
        
        print(f"[DEBUG-DETAIL] 開始重疊檢測，總共 {len(results)} 個結果")
        
        # 保存原始順序
        for i, result in enumerate(results):
            result['_original_order'] = i
            metadata = result.get('metadata', {})
            chunk_id = metadata.get('chunk_id', '?')
            file_name = metadata.get('file_name', result.get('source', 'unknown'))
            range_info = f"{metadata.get('start_char', 0)}-{metadata.get('end_char', 0)}"
            print(f"[DEBUG-DETAIL] 結果 {i}: 塊 {chunk_id} ({file_name}) 範圍 {range_info}")
        
        # 改為檢查所有結果對之間的重疊，不只是按順序檢查
        enhanced_results = []
        
        for i, current_result in enumerate(results):
            print(f"[DEBUG-DETAIL] \n=== 處理結果 {i} ===")
            
            # 檢查與所有其他結果的重疊
            other_results = results[:i] + results[i+1:]  # 所有其他結果
            overlap_info = self._analyze_result_overlap_with_all(current_result, other_results)
            enhanced_result = self._enhance_result_with_overlap(current_result, overlap_info)
            enhanced_results.append(enhanced_result)
        
        # 清理臨時字段
        for result in enhanced_results:
            result.pop('_original_order', None)
        
        overlap_count = sum(1 for r in enhanced_results if r.get('has_overlap', False))
        print(f"[DEBUG-DETAIL] 重疊檢測完成，找到 {overlap_count} 個有重疊的結果")
        
        return enhanced_results
    
    def _sort_results_by_position(self, results: List[Dict]) -> List[Dict]:
        """
        按文件名和字符位置排序結果
        
        Args:
            results: 原始結果列表
            
        Returns:
            排序後的結果列表
        """
        def sort_key(result):
            metadata = result.get('metadata', {})
            file_name = metadata.get('file_name', result.get('source', ''))
            start_char = metadata.get('start_char', 0)
            return (file_name, start_char)
        
        return sorted(results, key=sort_key)
    
    def _analyze_result_overlap(self, current_result: Dict, previous_results: List[Dict]) -> Dict:
        """
        分析當前結果與之前結果的重疊情況
        
        Args:
            current_result: 當前結果
            previous_results: 之前的結果列表
            
        Returns:
            重疊分析信息
        """
        current_text = current_result.get('text', '')
        current_metadata = current_result.get('metadata', {})
        current_file = current_metadata.get('file_name', current_result.get('source', ''))
        current_chunk = current_metadata.get('chunk_id', '?')
        current_range = f"{current_metadata.get('start_char', 0)}-{current_metadata.get('end_char', 0)}"
        
        print(f"[DEBUG-DETAIL] 分析塊 {current_chunk} ({current_range})，比對前 {len(previous_results)} 個結果")
        
        overlap_info = {
            'has_overlap': False,
            'overlap_sources': [],
            'overlap_regions': [],
            'unique_content': current_text,
            'overlap_content': '',
            'overlap_percentage': 0.0
        }
        
        for i, prev_result in enumerate(previous_results):
            prev_text = prev_result.get('text', '')
            prev_metadata = prev_result.get('metadata', {})
            prev_file = prev_metadata.get('file_name', prev_result.get('source', ''))
            prev_chunk = prev_metadata.get('chunk_id', '?')
            prev_range = f"{prev_metadata.get('start_char', 0)}-{prev_metadata.get('end_char', 0)}"
            
            print(f"[DEBUG-DETAIL]   比對塊 {current_chunk} vs 塊 {prev_chunk} ({prev_range}): 同文件={current_file == prev_file}")
            
            # 只檢測同文件內的重疊
            if current_file == prev_file:
                overlap_regions = self._find_text_overlaps(current_text, prev_text)
                print(f"[DEBUG-DETAIL]     文本重疊檢測結果: {len(overlap_regions) if overlap_regions else 0} 個重疊區域")
                
                if overlap_regions:
                    overlap_info['has_overlap'] = True
                    overlap_info['overlap_sources'].append({
                        'result_id': prev_result.get('id'),
                        'chunk_id': prev_metadata.get('chunk_id'),
                        'start_char': prev_metadata.get('start_char'),
                        'end_char': prev_metadata.get('end_char')
                    })
                    overlap_info['overlap_regions'].extend(overlap_regions)
                    print(f"[DEBUG-DETAIL]     ✅ 檢測到重疊！塊 {current_chunk} 與塊 {prev_chunk} 有重疊")
                else:
                    print(f"[DEBUG-DETAIL]     ❌ 未檢測到重疊：塊 {current_chunk} 與塊 {prev_chunk}")
        
        print(f"[DEBUG-DETAIL] 塊 {current_chunk} 重疊檢測總結: has_overlap={overlap_info['has_overlap']}, 重疊來源數={len(overlap_info['overlap_sources'])}")
        
        # 如果有重疊，計算重疊內容和獨有內容
        if overlap_info['has_overlap']:
            overlap_content, unique_content, overlap_percentage = self._extract_overlap_and_unique(
                current_text, overlap_info['overlap_regions']
            )
            overlap_info['overlap_content'] = overlap_content
            overlap_info['unique_content'] = unique_content
            overlap_info['overlap_percentage'] = overlap_percentage
        
        return overlap_info
    
    def _analyze_result_overlap_with_all(self, current_result: Dict, other_results: List[Dict]) -> Dict:
        """
        分析當前結果與所有其他結果的重疊情況
        
        Args:
            current_result: 當前結果
            other_results: 所有其他結果列表
            
        Returns:
            重疊分析信息
        """
        current_text = current_result.get('text', '')
        current_metadata = current_result.get('metadata', {})
        current_file = current_metadata.get('file_name', current_result.get('source', ''))
        current_chunk = current_metadata.get('chunk_id', '?')
        current_range = f"{current_metadata.get('start_char', 0)}-{current_metadata.get('end_char', 0)}"
        
        print(f"[DEBUG-DETAIL] 分析塊 {current_chunk} ({current_range})，比對所有 {len(other_results)} 個其他結果")
        
        overlap_info = {
            'has_overlap': False,
            'overlap_sources': [],
            'overlap_regions': [],
            'unique_content': current_text,
            'overlap_content': '',
            'overlap_percentage': 0.0
        }
        
        for i, other_result in enumerate(other_results):
            other_text = other_result.get('text', '')
            other_metadata = other_result.get('metadata', {})
            other_file = other_metadata.get('file_name', other_result.get('source', ''))
            other_chunk = other_metadata.get('chunk_id', '?')
            other_range = f"{other_metadata.get('start_char', 0)}-{other_metadata.get('end_char', 0)}"
            
            print(f"[DEBUG-DETAIL]   比對塊 {current_chunk} vs 塊 {other_chunk} ({other_range}): 同文件={current_file == other_file}")
            
            # 只檢測同文件內的重疊
            if current_file == other_file:
                overlap_regions = self._find_text_overlaps(current_text, other_text)
                print(f"[DEBUG-DETAIL]     文本重疊檢測結果: {len(overlap_regions) if overlap_regions else 0} 個重疊區域")
                
                if overlap_regions:
                    overlap_info['has_overlap'] = True
                    overlap_info['overlap_sources'].append({
                        'result_id': other_result.get('id'),
                        'chunk_id': other_metadata.get('chunk_id'),
                        'start_char': other_metadata.get('start_char'),
                        'end_char': other_metadata.get('end_char')
                    })
                    overlap_info['overlap_regions'].extend(overlap_regions)
                    print(f"[DEBUG-DETAIL]     ✅ 檢測到重疊！塊 {current_chunk} 與塊 {other_chunk} 有重疊")
                else:
                    print(f"[DEBUG-DETAIL]     ❌ 未檢測到重疊：塊 {current_chunk} 與塊 {other_chunk}")
        
        print(f"[DEBUG-DETAIL] 塊 {current_chunk} 重疊檢測總結: has_overlap={overlap_info['has_overlap']}, 重疊來源數={len(overlap_info['overlap_sources'])}")
        
        # 如果有重疊，計算重疊內容和獨有內容
        if overlap_info['has_overlap']:
            overlap_content, unique_content, overlap_percentage = self._extract_overlap_and_unique(
                current_text, overlap_info['overlap_regions']
            )
            overlap_info['overlap_content'] = overlap_content
            overlap_info['unique_content'] = unique_content
            overlap_info['overlap_percentage'] = overlap_percentage
        
        return overlap_info
    
    def _find_text_overlaps(self, text1: str, text2: str) -> List[Tuple[int, int, str]]:
        """
        找出兩個文本間的重疊區域
        
        Args:
            text1: 當前文本
            text2: 比較文本
            
        Returns:
            重疊區域列表 [(start, end, overlap_text), ...]
        """
        overlaps = []
        
        # 使用 SequenceMatcher 找出相似區域
        matcher = SequenceMatcher(None, text1, text2)
        matching_blocks = matcher.get_matching_blocks()
        
        for match in matching_blocks:
            start_a, start_b, length = match
            if length >= self.min_overlap_length:
                overlap_text = text1[start_a:start_a + length]
                # 清理重疊文本
                overlap_text = self._clean_overlap_text(overlap_text)
                if len(overlap_text) >= self.min_overlap_length:
                    overlaps.append((start_a, start_a + length, overlap_text))
        
        return overlaps
    
    def _clean_overlap_text(self, text: str) -> str:
        """
        清理重疊文本，移除過多空白和無意義字符
        
        Args:
            text: 原始重疊文本
            
        Returns:
            清理後的文本
        """
        # 統一空白字符
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # 移除過短的片段
        if len(cleaned) < self.min_overlap_length // 2:
            return ''
        
        return cleaned
    
    def _extract_overlap_and_unique(self, text: str, overlap_regions: List[Tuple[int, int, str]]) -> Tuple[str, str, float]:
        """
        提取重疊內容和獨有內容
        
        Args:
            text: 原始文本
            overlap_regions: 重疊區域列表
            
        Returns:
            (重疊內容, 獨有內容, 重疊百分比)
        """
        if not overlap_regions:
            return '', text, 0.0
        
        # 合並重疊區域
        merged_regions = self._merge_overlap_regions(overlap_regions)
        
        # 提取重疊內容
        overlap_parts = []
        unique_parts = []
        last_end = 0
        
        for start, end, _ in merged_regions:
            # 添加獨有部分（重疊區域前）
            if start > last_end:
                unique_parts.append(text[last_end:start])
            
            # 添加重疊部分
            overlap_parts.append(text[start:end])
            last_end = end
        
        # 添加最後的獨有部分
        if last_end < len(text):
            unique_parts.append(text[last_end:])
        
        overlap_content = ''.join(overlap_parts)
        unique_content = ''.join(unique_parts)
        overlap_percentage = len(overlap_content) / len(text) * 100 if text else 0
        
        return overlap_content, unique_content, overlap_percentage
    
    def _merge_overlap_regions(self, regions: List[Tuple[int, int, str]]) -> List[Tuple[int, int, str]]:
        """
        合並重疊或相鄰的區域
        
        Args:
            regions: 重疊區域列表
            
        Returns:
            合並後的區域列表
        """
        if not regions:
            return []
        
        # 按開始位置排序
        sorted_regions = sorted(regions, key=lambda x: x[0])
        merged = [sorted_regions[0]]
        
        for current in sorted_regions[1:]:
            last = merged[-1]
            
            # 檢查是否重疊或相鄰（允許小間隙）
            if current[0] <= last[1] + 20:  # 20字符的容忍間隙
                # 合併區域
                new_end = max(last[1], current[1])
                merged[-1] = (last[0], new_end, last[2])
            else:
                merged.append(current)
        
        return merged
    
    def _enhance_result_with_overlap(self, result: Dict, overlap_info: Dict) -> Dict:
        """
        使用重疊信息增強結果
        
        Args:
            result: 原始結果
            overlap_info: 重疊分析信息
            
        Returns:
            增強的結果
        """
        enhanced_result = result.copy()
        
        # 添加重疊檢測信息
        enhanced_result['has_overlap'] = overlap_info['has_overlap']
        enhanced_result['overlap_info'] = overlap_info
        
        # 如果有重疊，更新顯示相關字段
        if overlap_info['has_overlap']:
            enhanced_result['overlap_content'] = overlap_info['overlap_content']
            enhanced_result['unique_content'] = overlap_info['unique_content']
            enhanced_result['overlap_percentage'] = overlap_info['overlap_percentage']
            
            # 更新元數據
            metadata = enhanced_result.get('metadata', {})
            metadata['has_overlap'] = True
            metadata['overlap_percentage'] = overlap_info['overlap_percentage']
            enhanced_result['metadata'] = metadata
        
        return enhanced_result
    
    def _mark_no_overlap(self, results: List[Dict]) -> List[Dict]:
        """
        標記結果為無重疊
        
        Args:
            results: 結果列表
            
        Returns:
            標記後的結果列表
        """
        enhanced_results = []
        for result in results:
            enhanced_result = result.copy()
            enhanced_result['has_overlap'] = False
            enhanced_result['overlap_info'] = {
                'has_overlap': False,
                'overlap_sources': [],
                'overlap_regions': [],
                'unique_content': result.get('text', ''),
                'overlap_content': '',
                'overlap_percentage': 0.0
            }
            
            # 更新元數據
            metadata = enhanced_result.get('metadata', {})
            metadata['has_overlap'] = False
            enhanced_result['metadata'] = metadata
            
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    def get_overlap_statistics(self, results: List[Dict]) -> Dict:
        """
        獲取重疊統計信息
        
        Args:
            results: 檢測後的結果列表
            
        Returns:
            統計信息字典
        """
        total_results = len(results)
        overlap_results = sum(1 for r in results if r.get('has_overlap', False))
        
        overlap_percentages = [
            r.get('overlap_percentage', 0) 
            for r in results if r.get('has_overlap', False)
        ]
        
        avg_overlap_percentage = sum(overlap_percentages) / len(overlap_percentages) if overlap_percentages else 0
        
        return {
            'total_results': total_results,
            'overlap_results': overlap_results,
            'unique_results': total_results - overlap_results,
            'overlap_ratio': overlap_results / total_results if total_results > 0 else 0,
            'average_overlap_percentage': avg_overlap_percentage,
            'max_overlap_percentage': max(overlap_percentages) if overlap_percentages else 0
        }