#!/usr/bin/env python3
"""
測試程序立即退出的問題
"""

import sys
import os
import logging

# 設置日誌
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_input():
    """測試基本輸入功能"""
    print("=== 測試基本輸入 ===")
    try:
        # 測試 1: 簡單輸入
        print("測試 1: 請輸入任意文字...")
        user_input = input("輸入: ")
        print(f"你輸入了: '{user_input}'")
        
        # 測試 2: 選擇菜單
        print("\n測試 2: 選擇菜單")
        print("1. 選項一")
        print("2. 選項二")
        print("0. 退出")
        choice = input("請選擇 [0-2]: ")
        print(f"你選擇了: '{choice}'")
        
    except (KeyboardInterrupt, EOFError) as e:
        print(f"\n捕獲到異常: {type(e).__name__}")
        return False
    except Exception as e:
        print(f"\n發生錯誤: {e}")
        return False
    
    return True

def test_ui_components():
    """測試 UI 組件"""
    print("\n=== 測試 UI 組件 ===")
    try:
        from src.ui.ui_menu import MenuUI
        from src.ui.ui_base import BaseUI
        
        print("✓ UI 模組導入成功")
        
        # 創建假的搜索引擎
        class FakeSearchEngine:
            def __init__(self):
                logger.info("FakeSearchEngine initialized")
                
            def semantic_search(self, query, n_results):
                logger.info(f"semantic_search called with query='{query}', n_results={n_results}")
                return []
        
        # 測試 MenuUI
        fake_engine = FakeSearchEngine()
        menu_ui = MenuUI(fake_engine)
        print("✓ MenuUI 創建成功")
        
        # 測試 get_search_mode 方法
        print("\n測試 get_search_mode 方法")
        print("請輸入 1-7 的數字來測試搜索模式選擇:")
        
        # 手動調用 get_search_mode
        import io
        original_stdin = sys.stdin
        
        # 模擬輸入 '1'
        sys.stdin = io.StringIO('1\n')
        mode = menu_ui.get_search_mode()
        print(f"✓ get_search_mode 返回: '{mode}'")
        
        # 恢復原始 stdin
        sys.stdin = original_stdin
        
        # 測試 get_user_query 方法
        print("\n測試 get_user_query 方法")
        print("請輸入查詢內容:")
        
        # 模擬輸入查詢
        sys.stdin = io.StringIO('test query\n')
        query = menu_ui.get_user_query("輸入查詢: ")
        print(f"✓ get_user_query 返回: '{query}'")
        
        # 恢復原始 stdin
        sys.stdin = original_stdin
        
    except ImportError as e:
        print(f"✗ 導入錯誤: {e}")
        logger.error(f"Import error: {e}", exc_info=True)
        return False
    except Exception as e:
        print(f"✗ 錯誤: {e}")
        logger.error(f"Error: {e}", exc_info=True)
        return False
    
    return True

def test_search_execution():
    """測試搜索執行流程"""
    print("\n=== 測試搜索執行流程 ===")
    try:
        from src.ui import TongRAGInterface
        from src.search_engine import ChromaSearchEngine
        
        print("正在初始化搜索引擎...")
        search_engine = ChromaSearchEngine()
        print("✓ 搜索引擎初始化成功")
        
        print("正在創建界面...")
        interface = TongRAGInterface(search_engine)
        print("✓ 界面創建成功")
        
        # 模擬用戶輸入序列
        import io
        
        # 模擬: 選擇模式 1 -> 輸入查詢 "test" -> 返回菜單
        input_sequence = "1\ntest\nr\n0\n"
        sys.stdin = io.StringIO(input_sequence)
        
        print("\n開始模擬交互會話...")
        print("模擬輸入序列: 選擇模式 1 -> 輸入 'test' -> 返回 -> 退出")
        
        # 捕獲輸出
        from io import StringIO
        import contextlib
        
        output = StringIO()
        with contextlib.redirect_stdout(output):
            try:
                # 限制循環次數防止無限循環
                max_iterations = 10
                iteration = 0
                
                # 手動執行部分 run_interactive_session 邏輯
                while iteration < max_iterations:
                    iteration += 1
                    
                    mode = interface.get_search_mode()
                    print(f"Iteration {iteration}: Selected mode = '{mode}'")
                    
                    if mode == '0':
                        print("User selected exit")
                        break
                    
                    if mode in ['1', '2', '3', '4', '5', '6']:
                        # 這會調用 execute_search
                        query = interface.get_user_query("查詢: ")
                        print(f"Query received: '{query}'")
                        
                        if query == 'QUIT':
                            break
                    
                    if iteration >= 3:  # 防止無限循環
                        break
                        
            except EOFError:
                print("Reached end of input")
            except Exception as e:
                print(f"Error during simulation: {e}")
        
        # 恢復原始 stdin
        sys.stdin = sys.__stdin__
        
        output_text = output.getvalue()
        print("\n模擬會話輸出摘要:")
        lines = output_text.split('\n')[:20]  # 只顯示前20行
        for line in lines:
            if line.strip():
                print(f"  {line[:80]}")
        
        return True
        
    except Exception as e:
        print(f"✗ 測試失敗: {e}")
        logger.error(f"Test failed: {e}", exc_info=True)
        return False
    finally:
        # 確保恢復 stdin
        sys.stdin = sys.__stdin__

def main():
    """主測試函數"""
    print("=" * 60)
    print("TongRAG3 輸入問題診斷測試")
    print("=" * 60)
    
    # 測試 1: 基本輸入
    if not test_basic_input():
        print("\n✗ 基本輸入測試失敗")
        return 1
    
    # 測試 2: UI 組件
    if not test_ui_components():
        print("\n✗ UI 組件測試失敗")
        return 1
    
    # 測試 3: 搜索執行
    if not test_search_execution():
        print("\n✗ 搜索執行測試失敗")
        return 1
    
    print("\n" + "=" * 60)
    print("✓ 所有測試完成")
    print("=" * 60)
    return 0

if __name__ == "__main__":
    exit(main())