#!/usr/bin/env python3
"""
TongRAG3 重疊檢測系統統一化驗證腳本

本腳本用於驗證重疊檢測系統統一化後的效果，確保：
1. 所有搜索模式統一使用 OverlapDetector
2. 不再有 content_type == "overlap" 的舊系統痕跡
3. 重疊結果都有 content_type == "overlap_detected" 
4. 重疊結果包含正確的預覽字段
5. 所有結果顯示格式一致

作者: Claude Code (Test Automation Specialist)
日期: 2025-08-31
"""

import sys
import os
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple
import traceback

# 確保可以導入項目模組
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from src.search.engine import ChromaSearchEngine
    from src.utils import format_results
    from src.utils.overlap_detector import OverlapDetector
except ImportError as e:
    print(f"❌ 導入錯誤: {e}")
    print("請確保在正確的項目目錄中運行此腳本")
    sys.exit(1)


class OverlapConsistencyTester:
    """重疊檢測一致性測試器"""
    
    def __init__(self):
        self.search_engine = None
        self.test_results = []
        self.errors = []
        self.start_time = datetime.now()
        
        # 測試統計
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        
        # 測試查詢
        self.test_query = "MOS 規定"
        
        # 搜索模式定義
        self.search_modes = {
            1: {"name": "語義搜索", "method": "semantic_search"},
            2: {"name": "關鍵詞搜索", "method": "keyword_search"},
            3: {"name": "正則表達式搜索", "method": "regex_search"},
            4: {"name": "混合搜索", "method": "hybrid_search"},
            5: {"name": "元數據搜索", "method": "metadata_filter_search"},
            6: {"name": "語義搜索（含重排序）", "method": "semantic_search_with_reranking"},
            7: {"name": "對比模式", "method": "comparison_mode"}
        }
        
        print("🧪 TongRAG3 重疊檢測系統統一化驗證測試")
        print("=" * 60)
        print(f"測試時間: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"測試查詢: '{self.test_query}'")
        print("=" * 60)
    
    def initialize_search_engine(self) -> bool:
        """初始化搜索引擎"""
        try:
            print("📡 正在初始化搜索引擎...")
            self.search_engine = ChromaSearchEngine()
            print("✅ 搜索引擎初始化成功")
            return True
        except Exception as e:
            self.errors.append(f"搜索引擎初始化失敗: {str(e)}")
            print(f"❌ 搜索引擎初始化失敗: {str(e)}")
            return False
    
    def test_search_mode(self, mode_id: int, mode_info: Dict) -> Dict:
        """測試單個搜索模式"""
        mode_name = mode_info["name"]
        method_name = mode_info["method"]
        
        print(f"\n🔍 測試模式 {mode_id}: {mode_name}")
        print("-" * 40)
        
        test_result = {
            "mode_id": mode_id,
            "mode_name": mode_name,
            "method_name": method_name,
            "success": False,
            "total_results": 0,
            "overlap_results": 0,
            "unique_results": 0,
            "old_overlap_format": 0,
            "new_overlap_format": 0,
            "consistency_issues": [],
            "execution_time": 0,
            "error_message": None
        }
        
        try:
            start_time = time.time()
            
            # 執行搜索
            results = self._execute_search(method_name)
            
            if results is None:
                test_result["error_message"] = f"搜索方法 {method_name} 執行失敗"
                return test_result
            
            # 格式化結果（使用統一的format_results）
            formatted_results = format_results(results, enable_overlap_detection=True)
            
            # 分析結果
            test_result.update(self._analyze_results(formatted_results))
            test_result["execution_time"] = time.time() - start_time
            test_result["success"] = True
            
            # 報告結果
            self._report_mode_results(test_result)
            
        except Exception as e:
            test_result["error_message"] = str(e)
            print(f"❌ 執行失敗: {str(e)}")
            traceback.print_exc()
        
        return test_result
    
    def _execute_search(self, method_name: str) -> List[Dict]:
        """執行具體的搜索方法"""
        try:
            if method_name == "semantic_search":
                return self.search_engine.semantic_search(self.test_query, n_results=5)
            
            elif method_name == "keyword_search":
                return self.search_engine.keyword_search("MOS", n_results=5)
            
            elif method_name == "regex_search":
                return self.search_engine.regex_search(r"MOS.*規定", n_results=5)
            
            elif method_name == "hybrid_search":
                return self.search_engine.hybrid_search(self.test_query, n_results=5)
            
            elif method_name == "metadata_filter_search":
                return self.search_engine.metadata_filter_search(
                    self.test_query, 
                    filters={}, 
                    n_results=5
                )
            
            elif method_name == "semantic_search_with_reranking":
                return self.search_engine.semantic_search_with_reranking(
                    self.test_query, 
                    n_results=5
                )
            
            elif method_name == "comparison_mode":
                # 對比模式需要特殊處理
                original_results = self.search_engine.semantic_search(self.test_query, n_results=5)
                reranked_results = self.search_engine.semantic_search_with_reranking(self.test_query, n_results=5)
                return reranked_results if reranked_results else original_results
            
            else:
                print(f"⚠️ 未知的搜索方法: {method_name}")
                return []
                
        except Exception as e:
            print(f"❌ 搜索執行錯誤: {str(e)}")
            return None
    
    def _analyze_results(self, formatted_results: List[Dict]) -> Dict:
        """分析格式化後的結果"""
        analysis = {
            "total_results": len(formatted_results),
            "overlap_results": 0,
            "unique_results": 0,
            "old_overlap_format": 0,
            "new_overlap_format": 0,
            "consistency_issues": []
        }
        
        for i, result in enumerate(formatted_results):
            # 檢查content_type
            content_type = result.get("content_type", "")
            has_overlap = result.get("has_overlap", False)
            
            # 檢測舊系統痕跡
            if content_type == "overlap":
                analysis["old_overlap_format"] += 1
                analysis["consistency_issues"].append(
                    f"結果 {i+1}: 發現舊的 content_type='overlap'"
                )
            
            # 檢測新系統格式
            if content_type == "overlap_detected":
                analysis["new_overlap_format"] += 1
                analysis["overlap_results"] += 1
                
                # 驗證新系統必需字段
                if not result.get("overlap_preview"):
                    analysis["consistency_issues"].append(
                        f"結果 {i+1}: 缺少 overlap_preview 字段"
                    )
                
                if not result.get("unique_preview"):
                    analysis["consistency_issues"].append(
                        f"結果 {i+1}: 缺少 unique_preview 字段"
                    )
                
                if not result.get("overlap_info"):
                    analysis["consistency_issues"].append(
                        f"結果 {i+1}: 缺少 overlap_info 字段"
                    )
            
            # 檢查has_overlap標記一致性
            if has_overlap and content_type != "overlap_detected":
                analysis["consistency_issues"].append(
                    f"結果 {i+1}: has_overlap=True 但 content_type 不是 'overlap_detected'"
                )
            
            if not has_overlap and content_type == "overlap_detected":
                analysis["consistency_issues"].append(
                    f"結果 {i+1}: content_type='overlap_detected' 但 has_overlap=False"
                )
            
            # 統計獨有結果
            if not has_overlap:
                analysis["unique_results"] += 1
        
        return analysis
    
    def _report_mode_results(self, test_result: Dict):
        """報告單個模式的測試結果"""
        print(f"📊 結果總數: {test_result['total_results']}")
        print(f"🔗 重疊結果: {test_result['overlap_results']}")
        print(f"📄 獨有結果: {test_result['unique_results']}")
        print(f"⏱️ 執行時間: {test_result['execution_time']:.2f} 秒")
        
        # 檢查一致性
        if test_result['old_overlap_format'] > 0:
            print(f"⚠️ 發現 {test_result['old_overlap_format']} 個舊格式結果")
        
        if test_result['new_overlap_format'] > 0:
            print(f"✅ 發現 {test_result['new_overlap_format']} 個新格式重疊結果")
        
        if test_result['consistency_issues']:
            print(f"❌ 一致性問題: {len(test_result['consistency_issues'])}")
            for issue in test_result['consistency_issues']:
                print(f"   - {issue}")
        else:
            print("✅ 格式一致性檢查通過")
    
    def run_all_tests(self) -> Dict:
        """運行所有測試模式"""
        if not self.initialize_search_engine():
            return self.generate_final_report()
        
        print(f"\n🎯 開始測試所有 {len(self.search_modes)} 個搜索模式")
        print("=" * 60)
        
        # 逐個測試每個搜索模式
        for mode_id, mode_info in self.search_modes.items():
            self.total_tests += 1
            result = self.test_search_mode(mode_id, mode_info)
            self.test_results.append(result)
            
            if result["success"] and not result["consistency_issues"]:
                self.passed_tests += 1
                print("✅ 測試通過")
            else:
                self.failed_tests += 1
                print("❌ 測試失敗")
            
            time.sleep(0.5)  # 短暫暫停避免過於頻繁的搜索
        
        return self.generate_final_report()
    
    def generate_final_report(self) -> Dict:
        """生成最終測試報告"""
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        # 統計總體結果
        total_results = sum(r["total_results"] for r in self.test_results)
        total_overlap_results = sum(r["overlap_results"] for r in self.test_results)
        total_unique_results = sum(r["unique_results"] for r in self.test_results)
        total_old_format = sum(r["old_overlap_format"] for r in self.test_results)
        total_new_format = sum(r["new_overlap_format"] for r in self.test_results)
        total_issues = sum(len(r["consistency_issues"]) for r in self.test_results)
        
        # 生成報告
        report = {
            "test_summary": {
                "total_tests": self.total_tests,
                "passed_tests": self.passed_tests,
                "failed_tests": self.failed_tests,
                "success_rate": (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0,
                "total_duration": total_duration
            },
            "result_summary": {
                "total_search_results": total_results,
                "total_overlap_results": total_overlap_results,
                "total_unique_results": total_unique_results,
                "overlap_detection_rate": (total_overlap_results / total_results * 100) if total_results > 0 else 0
            },
            "consistency_check": {
                "old_format_results": total_old_format,
                "new_format_results": total_new_format,
                "total_issues": total_issues,
                "is_consistent": total_old_format == 0 and total_issues == 0
            },
            "detailed_results": self.test_results,
            "errors": self.errors,
            "timestamp": end_time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 輸出報告
        self._print_final_report(report)
        
        return report
    
    def _print_final_report(self, report: Dict):
        """打印最終報告"""
        print("\n" + "=" * 80)
        print("🎉 TongRAG3 重疊檢測系統統一化驗證報告")
        print("=" * 80)
        
        # 測試摘要
        summary = report["test_summary"]
        print(f"📈 測試摘要:")
        print(f"   總測試數: {summary['total_tests']}")
        print(f"   通過測試: {summary['passed_tests']}")
        print(f"   失敗測試: {summary['failed_tests']}")
        print(f"   成功率: {summary['success_rate']:.1f}%")
        print(f"   總耗時: {summary['total_duration']:.2f} 秒")
        
        # 結果摘要  
        result_summary = report["result_summary"]
        print(f"\n📊 結果摘要:")
        print(f"   總搜索結果: {result_summary['total_search_results']}")
        print(f"   重疊結果數: {result_summary['total_overlap_results']}")
        print(f"   獨有結果數: {result_summary['total_unique_results']}")
        print(f"   重疊檢測率: {result_summary['overlap_detection_rate']:.1f}%")
        
        # 一致性檢查
        consistency = report["consistency_check"]
        print(f"\n🔍 一致性檢查:")
        print(f"   舊格式結果: {consistency['old_format_results']}")
        print(f"   新格式結果: {consistency['new_format_results']}")
        print(f"   一致性問題: {consistency['total_issues']}")
        
        if consistency['is_consistent']:
            print("   ✅ 系統統一化完成！")
        else:
            print("   ❌ 發現一致性問題，需要修復")
        
        # 成功標準檢查
        print(f"\n🎯 成功標準檢查:")
        self._check_success_criteria(report)
        
        # 詳細問題列表
        if consistency['total_issues'] > 0:
            print(f"\n⚠️ 發現的問題:")
            for result in report["detailed_results"]:
                if result["consistency_issues"]:
                    print(f"   模式 {result['mode_id']} ({result['mode_name']}):")
                    for issue in result["consistency_issues"]:
                        print(f"     - {issue}")
        
        print("\n" + "=" * 80)
        print(f"報告生成時間: {report['timestamp']}")
        print("=" * 80)
    
    def _check_success_criteria(self, report: Dict):
        """檢查成功標準"""
        criteria = [
            ("所有搜索模式統一使用 OverlapDetector", self._check_overlap_detector_usage(report)),
            ("不再有 content_type == 'overlap' 的舊系統痕跡", report["consistency_check"]["old_format_results"] == 0),
            ("重疊結果都顯示完整的分區內容", self._check_overlap_display_format(report)),
            ("測試顯示所有結果格式一致", report["consistency_check"]["total_issues"] == 0),
            ("虛擬環境測試全部通過", report["test_summary"]["failed_tests"] == 0)
        ]
        
        for criterion, passed in criteria:
            status = "✅" if passed else "❌"
            print(f"   {status} {criterion}")
    
    def _check_overlap_detector_usage(self, report: Dict) -> bool:
        """檢查是否所有模式都使用了OverlapDetector"""
        # 如果有重疊結果，說明使用了OverlapDetector
        return report["result_summary"]["total_overlap_results"] > 0 or report["result_summary"]["total_search_results"] > 0
    
    def _check_overlap_display_format(self, report: Dict) -> bool:
        """檢查重疊顯示格式是否正確"""
        # 檢查所有重疊結果是否都有正確的格式
        for result in report["detailed_results"]:
            if result["overlap_results"] > 0 and result["consistency_issues"]:
                # 如果有重疊結果但有一致性問題，說明格式不正確
                return False
        return True


def main():
    """主函數"""
    tester = OverlapConsistencyTester()
    report = tester.run_all_tests()
    
    # 保存報告到文件
    try:
        import json
        report_filename = f"overlap_consistency_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"\n📄 詳細報告已保存到: {report_filename}")
    except Exception as e:
        print(f"⚠️ 報告保存失敗: {str(e)}")
    
    # 返回退出碼
    exit_code = 0 if report["consistency_check"]["is_consistent"] else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    main()