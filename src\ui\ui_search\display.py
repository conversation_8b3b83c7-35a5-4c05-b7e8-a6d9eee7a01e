"""
搜索結果顯示模組

處理所有搜索結果的顯示邏輯，包括：
- 結果格式化和展示
- 分頁控制
- 詳情查看
- 完整內容顯示
"""

from typing import Dict, List, Any
from ..ui_base import BaseUI
from ...utils import get_search_stats, export_results


class DisplayMixin(BaseUI):
    """
    結果顯示混合類
    
    提供所有搜索結果顯示相關功能
    """
    
    def display_results(self, results: List[Dict]) -> None:
        """
        展示搜索結果
        
        Args:
            results: 搜索結果列表
        """
        if not results:
            self.print_colored("未找到匹配的結果", "yellow")
            return
        
        # 格式化結果
        from ...utils import format_results
        formatted_results = format_results(results)
        self.last_results = formatted_results
        
        # 計算統計信息
        stats = get_search_stats(formatted_results)
        
        # 顯示統計信息
        self.print_colored(f"\n找到 {stats['total_results']} 個結果", "green")
        self.print_colored(f"平均相關度: {stats['average_score']:.1f}%", "cyan")
        
        if stats['source_count'] > 0:
            print(f"來源分布: {', '.join([f'{k}({v})' for k, v in list(stats['sources'].items())[:3]])}")
        print()
        
        # 分頁顯示結果
        self.display_paginated_results(formatted_results)
    
    def display_comparison_results(self, original_results: List[Dict], reranked_results: List[Dict], ranking_changes: Dict = None) -> None:
        """
        顯示對比結果（原始 vs 重排序）
        
        Args:
            original_results: 原始搜索結果
            reranked_results: 重排序結果
            ranking_changes: 排名變化信息
        """
        if not reranked_results:
            self.print_colored("重排序後未找到匹配的結果", "yellow")
            return
        
        from ...utils import format_results
        formatted_results = format_results(reranked_results)
        self.last_results = formatted_results
        self.last_original_results = format_results(original_results) if original_results else []
        
        # 計算統計信息
        stats = get_search_stats(formatted_results)
        
        # 顯示統計信息
        self.print_colored(f"\n重排序後找到 {stats['total_results']} 個結果", "green")
        self.print_colored(f"平均相關度: {stats['average_score']:.1f}%", "cyan")
        
        # 顯示排名改進信息
        if ranking_changes:
            improved = ranking_changes.get('improved', 0)
            declined = ranking_changes.get('declined', 0)
            unchanged = ranking_changes.get('unchanged', 0)
            
            self.print_colored(f"排名改進: ↑{improved} ↓{declined} ={unchanged}", "blue")
        
        print()
        
        # 分頁顯示結果（帶排名變化指示）
        self.display_paginated_results_with_changes(formatted_results, ranking_changes)
    
    def display_paginated_results(self, results: List[Dict]) -> None:
        """
        分頁顯示搜索結果
        
        Args:
            results: 格式化後的搜索結果
        """
        total_pages = (len(results) + self.page_size - 1) // self.page_size
        self.current_page = 0
        
        while True:
            start_idx = self.current_page * self.page_size
            end_idx = min(start_idx + self.page_size, len(results))
            page_results = results[start_idx:end_idx]
            
            # 顯示當前頁結果
            self.print_colored(f"=== 第 {self.current_page + 1} 頁，共 {total_pages} 頁 ===", "cyan")
            
            for result in page_results:
                self.display_single_result(result)
            
            # 顯示分頁控制和查看完整內容選項
            print("\n" + "=" * 60)
            
            # 動態生成查看完整內容提示
            result_count = len(page_results)
            if result_count > 0:
                start_num = start_idx + 1
                end_num = start_idx + result_count
                if result_count == 1:
                    print(f"輸入 {start_num} 查看完整內容")
                else:
                    print(f"輸入 {start_num}-{end_num} 查看指定編號的完整內容")
            
            # 分頁控制選項
            options = []
            if self.current_page > 0:
                options.append("p) 上一頁")
            if self.current_page < total_pages - 1:
                options.append("n) 下一頁")
            options.extend(["d) 查看詳情", "e) 導出結果", "r) 返回菜單"])
            
            print(" | ".join(options))
            print("=" * 60)
            
            choice = input("選擇操作: ").strip().lower()
            
            # 處理數字輸入（查看完整內容）
            if choice.isdigit():
                selected_num = int(choice)
                # 檢查是否在有效範圍內
                if result_count > 0:
                    start_num = start_idx + 1
                    end_num = start_idx + result_count
                    if start_num <= selected_num <= end_num:
                        idx = selected_num - start_num
                        self.show_full_content(page_results[idx])
                        continue
            
            # 處理其他選項
            if choice == 'p' and self.current_page > 0:
                self.current_page -= 1
            elif choice == 'n' and self.current_page < total_pages - 1:
                self.current_page += 1
            elif choice == 'd':
                self.show_result_details(page_results)
            elif choice == 'e':
                self.export_current_results()
                break
            elif choice == 'r':
                break
            else:
                if total_pages == 1:
                    break

    def display_paginated_results_with_changes(self, results: List[Dict], ranking_changes: Dict = None) -> None:
        """
        分頁顯示帶排名變化指示的搜索結果
        
        Args:
            results: 格式化後的搜索結果
            ranking_changes: 排名變化信息
        """
        total_pages = (len(results) + self.page_size - 1) // self.page_size
        self.current_page = 0
        
        while True:
            start_idx = self.current_page * self.page_size
            end_idx = min(start_idx + self.page_size, len(results))
            page_results = results[start_idx:end_idx]
            
            # 顯示當前頁結果
            self.print_colored(f"=== 第 {self.current_page + 1} 頁，共 {total_pages} 頁 (重排序結果) ===", "cyan")
            
            for result in page_results:
                self.display_single_result_with_changes(result, ranking_changes)
            
            # 顯示分頁控制
            print("\n" + "=" * 60)
            
            # 動態生成查看完整內容提示
            result_count = len(page_results)
            if result_count > 0:
                start_num = start_idx + 1
                end_num = start_idx + result_count
                if result_count == 1:
                    print(f"輸入 {start_num} 查看完整內容")
                else:
                    print(f"輸入 {start_num}-{end_num} 查看指定編號的完整內容")
            
            # 分頁控制選項
            options = []
            if self.current_page > 0:
                options.append("p) 上一頁")
            if self.current_page < total_pages - 1:
                options.append("n) 下一頁")
            options.extend(["d) 查看詳情", "c) 詳細對比", "e) 導出結果", "r) 返回菜單"])
            
            print(" | ".join(options))
            print("=" * 60)
            
            choice = input("選擇操作: ").strip().lower()
            
            # 處理數字輸入（查看完整內容）
            if choice.isdigit():
                selected_num = int(choice)
                if result_count > 0:
                    start_num = start_idx + 1
                    end_num = start_idx + result_count
                    if start_num <= selected_num <= end_num:
                        idx = selected_num - start_num
                        self.show_full_content(page_results[idx])
                        continue
            
            # 處理其他選項
            if choice == 'p' and self.current_page > 0:
                self.current_page -= 1
            elif choice == 'n' and self.current_page < total_pages - 1:
                self.current_page += 1
            elif choice == 'd':
                self.show_result_details(page_results)
            elif choice == 'c':
                self.show_detailed_comparison()
            elif choice == 'e':
                self.export_current_results()
                break
            elif choice == 'r':
                break
            else:
                if total_pages == 1:
                    break

    def display_single_result(self, result: Dict) -> None:
        """
        顯示單個搜索結果，支持重疊區域和獨有區域的區分顯示
        
        Args:
            result: 單個搜索結果字典
        """
        separator = "-" * 60
        print(separator)
        
        # 標題行
        rank = result.get("rank", 0)
        score = result.get("score_display", "N/A")
        source = result.get("source", "未知來源")
        
        # 檢查是否有重疊資訊
        has_overlap = result.get("has_overlap", False)
        chunk_id = result.get("chunk_id", 0)
        file_name = result.get("file_name", source)
        
        # 強制所有結果使用黃色標題和🔗標記
        overlap_marker = " 🔗"
        title_color = "yellow"
        self.print_colored(f"[{rank}]{overlap_marker} 相關度: {score} | 來源: {file_name}", title_color)
        
        # 總是顯示重疊來源信息（如果有的話）
        overlap_source_info = result.get("overlap_source_info", "")
        if overlap_source_info:
            self.print_colored(f"    🔗 [重疊區域]", "cyan")
            print(f"       來源塊: {overlap_source_info}")
        
        print("內容預覽:")
        
        # 強制使用重疊格式顯示所有結果
        print(f"[DEBUG] 顯示結果，content_type: {result.get('content_type', 'main')}")
        # 移除條件檢查，總是使用重疊格式
        self._display_overlap_sections(result)
        
        print()

    def _display_overlap_sections(self, result: Dict) -> None:
        """
        顯示重疊區域和獨有區域
        
        Args:
            result: 包含重疊檢測信息的結果
        """
        # 顯示重疊區域
        overlap_preview = result.get("overlap_preview", "")
        overlap_remaining = result.get("overlap_remaining_chars", 0)
        
        if overlap_preview:
            self.print_colored("    🟨 重疊區:", "white")
            if overlap_remaining > 0:
                overlap_display = f"{overlap_preview}"
            else:
                overlap_display = overlap_preview
            print(f"              {overlap_display}")
        
        # 顯示獨有區域
        unique_preview = result.get("unique_preview", "")
        unique_remaining = result.get("unique_remaining_chars", 0)
        
        if unique_preview:
            self.print_colored("    🟩 一般區:", "green")
            if unique_remaining > 0:
                unique_display = f"{unique_preview}（還有 {unique_remaining} 個字）"
            else:
                unique_display = unique_preview
            print(f"              {unique_display}")

    def display_single_result_with_changes(self, result: Dict, ranking_changes: Dict = None) -> None:
        """
        顯示帶排名變化指示的單個搜索結果，支持重疊區域和獨有區域的區分顯示
        
        Args:
            result: 單個搜索結果字典
            ranking_changes: 排名變化信息
        """
        separator = "-" * 60
        print(separator)
        
        # 標題行
        rank = result.get("rank", 0)
        score = result.get("score_display", "N/A")
        source = result.get("source", "未知來源")
        
        # 檢查是否有重疊資訊
        has_overlap = result.get("has_overlap", False)
        chunk_id = result.get("chunk_id", 0)
        file_name = result.get("file_name", source)
        
        # 添加排名變化指示
        rank_indicator = ""
        if ranking_changes and 'position_changes' in ranking_changes:
            result_id = result.get('id', '')
            if result_id in ranking_changes['position_changes']:
                change = ranking_changes['position_changes'][result_id]
                if change > 0:
                    rank_indicator = f" ↑{change}"
                elif change < 0:
                    rank_indicator = f" ↓{abs(change)}"
                else:
                    rank_indicator = " ="
        
        # 強制所有結果使用黃色標題和🔗標記（保留排名變化指示）
        overlap_marker = " 🔗"
        title_color = "yellow"
        self.print_colored(f"[{rank}]{rank_indicator}{overlap_marker} 相關度: {score} | 來源: {file_name}", title_color)
        
        # 總是顯示重疊來源信息（如果有的話）
        overlap_source_info = result.get("overlap_source_info", "")
        if overlap_source_info:
            self.print_colored(f"    🔗 [重疊區域]", "cyan")
            print(f"       來源塊: {overlap_source_info}")
        
        print("內容預覽:")
        
        # 強制使用重疊格式顯示所有結果
        print(f"[DEBUG] 顯示結果，content_type: {result.get('content_type', 'main')}")
        # 移除條件檢查，總是使用重疊格式
        self._display_overlap_sections(result)
        
        print()
    
    def show_result_details(self, results: List[Dict]) -> None:
        """
        顯示結果詳情
        
        Args:
            results: 當前頁面的搜索結果
        """
        if not results:
            return
        
        try:
            choice = input("輸入要查看詳情的結果編號: ").strip()
            idx = int(choice) - 1
            
            if 0 <= idx < len(results):
                result = results[idx]
                self.clear_screen()
                
                self.print_colored("=== 詳細內容 ===", "cyan")
                print(f"ID: {result.get('id', 'N/A')}")
                print(f"來源: {result.get('source', 'N/A')}")
                print(f"相關度分數: {result.get('score_display', 'N/A')}")
                print(f"距離值: {result.get('distance', 'N/A'):.4f}")
                print()
                
                self.print_colored("完整內容:", "yellow")
                text = result.get("text", "")
                if text:
                    print(text)
                else:
                    print("無內容")
                
                print("\n" + "=" * 60)
                input("按 Enter 返回...")
            else:
                self.print_colored("無效的結果編號", "red")
        except (ValueError, KeyboardInterrupt):
            self.print_colored("操作取消", "yellow")
    
    def show_full_content(self, result: Dict) -> None:
        """
        顯示搜索結果的完整內容，支持重疊區域和獨有區域的分區顯示
        
        Args:
            result: 搜索結果字典
        """
        self.clear_screen()
        self.print_colored("=== 完整內容 ===", "cyan")
        
        # 顯示基本信息
        print(f"編號: [{result.get('rank', 'N/A')}]")
        print(f"來源: {result.get('source', 'N/A')}")
        print(f"相關度: {result.get('score_display', 'N/A')}")
        print(f"內容類型: {result.get('content_description', '內容')}")
        
        if result.get('chunk_info'):
            print(f"塊信息: {result.get('chunk_info')}")
        
        # 顯示距離值
        distance = result.get('distance', 'N/A')
        if isinstance(distance, float):
            print(f"距離值: {distance:.4f}")
        else:
            print(f"距離值: {distance}")
        
        # 總是顯示重疊統計信息（即使沒有實際重疊）
        overlap_percentage = result.get('overlap_percentage', 0)
        print(f"重疊百分比: {overlap_percentage:.1f}%")
        
        overlap_source_info = result.get("overlap_source_info", "")
        if overlap_source_info:
            print(f"重疊來源: {overlap_source_info}")
        
        print("\n" + "-" * 60 + "\n")
        
        # 強制使用重疊格式顯示完整內容
        print(f"[DEBUG] 顯示完整內容，content_type: {result.get('content_type', 'main')}")
        # 移除條件檢查，總是使用重疊格式
        self._show_full_content_with_overlap(result)
        
        print("\n" + "=" * 60)
        
        # 顯示查看選項 - 總是提供重疊格式選項
        print("選項: [Enter] 返回 | [1] 僅重疊區 | [2] 僅獨有區 | [3] 全部內容")
        choice = input("選擇查看模式: ").strip()
        
        if choice == '1':
            self._show_overlap_only(result)
        elif choice == '2':
            self._show_unique_only(result)
        elif choice == '3':
            self._show_combined_content(result)
            input("按 Enter 返回搜索結果...")

    def _show_full_content_with_overlap(self, result: Dict) -> None:
        """
        顯示包含重疊檢測的完整內容
        
        Args:
            result: 搜索結果字典
        """
        overlap_info = result.get('overlap_info', {})
        overlap_content = overlap_info.get('overlap_content', '')
        unique_content = overlap_info.get('unique_content', '')
        
        # 顯示重疊區域
        if overlap_content:
            self.print_colored("🟨 重疊區域（完整）:", "white")
            print(f"{overlap_content}")
            print()
        
        # 顯示獨有區域  
        if unique_content:
            self.print_colored("🟩 獨有區域（完整）:", "green")
            print(f"{unique_content}")
            print()
        
        # 顯示組合內容預覽
        self.print_colored("📝 全部組合:", "blue")
        full_text = result.get('full_text') or result.get('text', '')
        if full_text:
            # 截斷顯示，避免內容過長
            if len(full_text) > 500:
                truncated_text = full_text[:500] + "..."
                print(f"{truncated_text}")
                print(f"（完整內容共 {len(full_text)} 個字符）")
            else:
                print(full_text)

    def _show_overlap_only(self, result: Dict) -> None:
        """顯示僅重疊區域"""
        self.clear_screen()
        self.print_colored("=== 重疊區域 ===", "white")
        
        overlap_info = result.get('overlap_info', {})
        overlap_content = overlap_info.get('overlap_content', '')
        
        if overlap_content:
            print(overlap_content)
        else:
            print("無重疊內容")
        
        print("\n" + "=" * 60)
        input("按 Enter 返回...")

    def _show_unique_only(self, result: Dict) -> None:
        """顯示僅獨有區域"""
        self.clear_screen()
        self.print_colored("=== 獨有區域 ===", "green")
        
        overlap_info = result.get('overlap_info', {})
        unique_content = overlap_info.get('unique_content', '')
        
        if unique_content:
            print(unique_content)
        else:
            print("無獨有內容")
        
        print("\n" + "=" * 60)
        input("按 Enter 返回...")

    def _show_combined_content(self, result: Dict) -> None:
        """顯示全部組合內容"""
        self.clear_screen()
        self.print_colored("=== 全部內容 ===", "blue")
        
        full_text = result.get('full_text') or result.get('text', '')
        if full_text:
            print(full_text)
        else:
            print("無完整內容")
        
        print("\n" + "=" * 60)
        input("按 Enter 返回...")

    def export_current_results(self) -> None:
        """導出當前搜索結果"""
        if not self.last_results:
            self.print_colored("沒有可導出的結果", "yellow")
            return
        
        print("選擇導出格式:")
        print("1. JSON")
        print("2. CSV") 
        print("3. TXT")
        
        try:
            choice = input("請選擇 [1-3]: ").strip()
            format_map = {"1": "json", "2": "csv", "3": "txt"}
            
            if choice not in format_map:
                self.print_colored("無效選擇", "red")
                return
            
            export_format = format_map[choice]
            filename = input("輸入文件名（留空自動生成）: ").strip()
            
            try:
                file_path = export_results(self.last_results, export_format, filename)
                self.print_colored(f"結果已導出到: {file_path}", "green")
            except Exception as e:
                self.print_colored(f"導出失敗: {str(e)}", "red")
                
        except (KeyboardInterrupt, EOFError):
            self.print_colored("導出取消", "yellow")