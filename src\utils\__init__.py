"""
Utils package for TongRAG3

This package contains utility functions and classes for the TongRAG3 system.
"""

# 導入 OverlapDetector，不要任何備用機制
# 如果導入失敗就直接報錯，不要靜默處理
from .overlap_detector import OverlapDetector

# 直接導入函數並重新導出，避免循環導入
import importlib.util
import os

# 直接讀取上層 utils.py 檔案
utils_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils.py')
spec = importlib.util.spec_from_file_location("utils_module", utils_path)
utils_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(utils_module)

# 重新導出需要的函數
convert_english_to_uppercase = utils_module.convert_english_to_uppercase
validate_query = utils_module.validate_query
export_results = utils_module.export_results
clean_text = utils_module.clean_text
truncate_text = utils_module.truncate_text
truncate_text_with_count = utils_module.truncate_text_with_count
calculate_relevance_score = utils_module.calculate_relevance_score
get_search_stats = utils_module.get_search_stats
format_results = utils_module.format_results

__all__ = [
    'OverlapDetector',
    'convert_english_to_uppercase',
    'validate_query',
    'export_results',
    'clean_text',
    'truncate_text',
    'truncate_text_with_count',
    'calculate_relevance_score',
    'get_search_stats',
    'format_results'
]