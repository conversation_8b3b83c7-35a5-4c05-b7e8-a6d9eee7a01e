"""
Search Engine Core Module

This module implements the main ChromaSearchEngine class that combines all
search functionalities from specialized mixins while maintaining full backward
compatibility with the original implementation.
"""

import logging
from typing import Dict, List, Optional, Any
import chromadb
from chromadb.api.models.Collection import Collection

from ..config import (
    CHROMA_DATA_PATH, COLLECTION_NAME, EMBEDDING_MODEL, 
    EMBEDDING_DIMENSION, MAX_RESULTS
)
from ..utils import convert_english_to_uppercase, format_results

# Import mixins for modular functionality
from .semantic import SemanticSearchMixin
from .keyword import KeywordSearchMixin
from .hybrid import HybridSearchMixin

# 設置日志
logger = logging.getLogger(__name__)


class ChromaSearchEngine(SemanticSearchMixin, KeywordSearchMixin, HybridSearchMixin):
    """
    基於 ChromaDB 的文檔搜索引擎類，提供多種搜索方法：
    - 向量相似度搜索（使用 ChromaDB 內建文本嵌入）
    - 關鍵詞精確匹配搜索
    - 正則表達式模式搜索
    - 元數據過濾搜索
    - 混合搜索（向量相似度 + 關鍵詞匹配）
    - 可選的重排序功能提升搜索質量
    
    ChromaDB 使用默認的文本嵌入模型將文本轉換為 384 維向量，
    通過餘弦相似度進行文檔檢索。
    
    新增功能：
    - 集成重排序器支援，可選擇性改善搜索結果質量
    - 模組化設計，便於維護和擴展
    - 保持 100% 向後兼容性
    """

    def __init__(self):
        """
        初始化 ChromaSearchEngine 實例。
        
        使用 PersistentClient 連接到本地 Chroma 數據庫。
        """
        # Initialize mixins
        super().__init__()
        
        # Core attributes
        self.client: Optional[chromadb.PersistentClient] = None
        self.collection: Optional[Collection] = None
        self.embedding_dim: int = EMBEDDING_DIMENSION  # BGE-M3 嵌入維度（1024）
        
        self._connect_to_chroma()

    def _connect_to_chroma(self) -> None:
        """連接到本地 Chroma 數據庫並創建或獲取集合。"""
        try:
            # 連接到本地 Chroma 數據庫，使用默認嵌入函數
            self.client = chromadb.PersistentClient(
                path=CHROMA_DATA_PATH
            )
            
            # 創建或獲取集合 - 使用默認嵌入函數
            try:
                self.collection = self.client.get_collection(
                    name=COLLECTION_NAME
                )
                logger.info(f"成功連接到現有集合: {COLLECTION_NAME} (默認嵌入)")
            except Exception:
                # 如果集合不存在，創建新集合
                self.collection = self.client.create_collection(
                    name=COLLECTION_NAME,
                    metadata={"hnsw:space": "cosine"}
                )
                logger.info(f"創建新集合: {COLLECTION_NAME}")
                
        except Exception as e:
            logger.error(f"連接到本地 Chroma 數據庫失敗: {str(e)}")
            raise ConnectionError(f"無法連接到本地 Chroma 數據庫: {str(e)}")

    def _ensure_connection(self) -> None:
        """確保 Chroma 連接和集合存在。"""
        if self.client is None or self.collection is None:
            self._connect_to_chroma()

    def _format_search_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        格式化搜索結果為統一格式。
        
        Args:
            results: Chroma 查詢返回的原始結果
            
        Returns:
            格式化後的結果列表
        """
        # 轉換為標準格式
        formatted_list = []
        if results and results.get('ids'):
            ids = results['ids'][0] if results['ids'] else []
            documents = results['documents'][0] if results.get('documents') else []
            metadatas = results['metadatas'][0] if results.get('metadatas') else []
            distances = results['distances'][0] if results.get('distances') else []
            
            for i, doc_id in enumerate(ids):
                result = {
                    "id": doc_id,
                    "text": documents[i] if i < len(documents) else "",
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                    "source": metadatas[i].get("source", "") if i < len(metadatas) and metadatas[i] else ""
                }
                formatted_list.append(result)
        
        return format_results(formatted_list)

    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        批量添加文檔到集合中。
        
        Args:
            documents: 文檔列表，每個文檔包含 id, text, metadata
            
        Returns:
            是否添加成功
        """
        try:
            self._ensure_connection()
            
            if not documents:
                logger.warning("沒有文檔需要添加")
                return False
            
            # 提取文檔數據，生成全局唯一ID
            ids = []
            for i, doc in enumerate(documents):
                # 嘗試使用 chunk_id 和 file_name 生成唯一ID
                if "chunk_id" in doc and "file_name" in doc:
                    unique_id = f"{doc['file_name']}_chunk_{doc['chunk_id']}"
                elif "chunk_id" in doc:
                    unique_id = f"chunk_{doc['chunk_id']}_{i}"
                else:
                    unique_id = f"doc_{i}"
                ids.append(unique_id)
            texts = [doc.get("text", "") for doc in documents]
            # 為每個文檔生成 metadata，包含文件信息和位置資訊
            metadatas = []
            for doc in documents:
                # 總是創建完整的metadata，確保所有必要欄位都存在
                metadata = {
                    "file_name": doc.get("file_name", "unknown"),
                    "chunk_id": doc.get("chunk_id", 0),
                    "file_path": doc.get("file_path", ""),
                    "created_time": doc.get("created_time", ""),
                    "file_size": doc.get("file_size", 0),
                    "start_char": doc.get("start_char", 0),      # 添加起始位置
                    "end_char": doc.get("end_char", 0)           # 添加結束位置
                }
                
                # 如果doc中有額外的metadata，合併進去
                if "metadata" in doc and doc["metadata"]:
                    metadata.update(doc["metadata"])
                    
                metadatas.append(metadata)
            
            # 批量添加文檔
            self.collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"成功添加 {len(documents)} 個文檔")
            return True
            
        except Exception as e:
            logger.error(f"添加文檔失敗: {str(e)}")
            return False

    def metadata_filter_search(
        self, 
        query: str, 
        filters: Dict[str, Any], 
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        元數據過濾搜索，基於向量相似度搜索並應用元數據過濾。
        
        使用 ChromaDB 的向量相似度搜索功能，同時根據文檔的元數據
        (如來源、類型等) 進行過濾。
        
        Args:
            query: 搜索查詢
            filters: 元數據過濾條件
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            self._ensure_connection()
            
            if not query.strip():
                return []
            
            # 將英文字母轉換為大寫
            processed_query = convert_english_to_uppercase(query)
            logger.debug(f"元數據過濾搜索 - 原查詢: '{query}' -> 處理後: '{processed_query}'")
            
            n_results = min(n_results, MAX_RESULTS)
            
            # 只有在有過濾條件時才應用 where 參數
            if filters:
                results = self.collection.query(
                    query_texts=[processed_query],
                    n_results=n_results,
                    where=filters
                )
            else:
                results = self.collection.query(
                    query_texts=[processed_query],
                    n_results=n_results
                )
            
            formatted_results = self._format_search_results(results)
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled():
                logger.debug("Applying reranking to metadata filter search results")
                formatted_results = self._apply_reranking(
                    query=processed_query,
                    search_results=formatted_results,
                    reranker_name=reranker_name,
                    top_k=n_results,
                    score_threshold=score_threshold
                )
            
            logger.debug(f"元數據過濾搜索完成: 返回 {len(formatted_results)} 個結果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"元數據過濾搜索失敗: {str(e)}")
            return []

    def get_by_ids(self, ids: List[str]) -> List[Dict[str, Any]]:
        """
        根據 ID 列表獲取文檔。
        
        Args:
            ids: 文檔 ID 列表
            
        Returns:
            文檔列表
        """
        try:
            self._ensure_connection()
            
            if not ids:
                return []
            
            results = self.collection.get(ids=ids)
            
            formatted_results = []
            if results and results.get('ids'):
                doc_ids = results['ids']
                documents = results.get('documents', [])
                metadatas = results.get('metadatas', [])
                
                for i, doc_id in enumerate(doc_ids):
                    result = {
                        "id": doc_id,
                        "text": documents[i] if i < len(documents) else "",
                        "metadata": metadatas[i] if i < len(metadatas) else {},
                        "distance": 0.0,  # ID 查詢沒有距離
                        "source": metadatas[i].get("source", "") if i < len(metadatas) and metadatas[i] else ""
                    }
                    formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"根據 ID 獲取文檔失敗: {str(e)}")
            return []

    def get_collection_info(self) -> Dict[str, Any]:
        """
        獲取集合信息。
        
        Returns:
            集合信息字典
        """
        try:
            self._ensure_connection()
            
            count = self.collection.count()
            info = {
                "name": COLLECTION_NAME,
                "count": count,
                "status": "connected",
                "embedding_model": EMBEDDING_MODEL,
                "embedding_dimension": EMBEDDING_DIMENSION
            }
            
            # Add reranker information if available
            reranker_info = self.get_reranker_info()
            if reranker_info:
                info["reranker"] = reranker_info
            
            return info
            
        except Exception as e:
            logger.error(f"獲取集合信息失敗: {str(e)}")
            return {
                "name": COLLECTION_NAME,
                "count": 0,
                "status": "error",
                "error": str(e)
            }

    def clear_collection(self) -> bool:
        """
        清空集合中的所有文檔。
        
        Returns:
            是否清空成功
        """
        try:
            self._ensure_connection()
            
            # 獲取所有文檔 ID
            all_docs = self.collection.get()
            if all_docs and all_docs.get('ids'):
                self.collection.delete(ids=all_docs['ids'])
                logger.info("成功清空集合")
                return True
            
            logger.info("集合已為空")
            return True
            
        except Exception as e:
            logger.error(f"清空集合失敗: {str(e)}")
            return False
    
    def close(self) -> None:
        """
        關閉 ChromaDB 連接並清理資源。
        """
        try:
            if self.client:
                # ChromaDB PersistentClient 不需要顯式關閉
                # 只需要清空引用即可
                self.client = None
                self.collection = None
                logger.info("ChromaDB 連接已關閉")
        except Exception as e:
            logger.error(f"關閉 ChromaDB 連接時出錯: {str(e)}")

    # Enhanced methods with optional reranker support
    # Note: The core search methods (semantic_search, keyword_search, hybrid_search, etc.)
    # are now inherited from their respective mixins and already include reranker support
    
    def bulk_search(
        self,
        queries: List[str],
        search_type: str = "semantic",
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0,
        **kwargs
    ) -> List[List[Dict[str, Any]]]:
        """
        批量搜索，支援多種搜索類型。
        
        Args:
            queries: 查詢列表
            search_type: 搜索類型 ("semantic", "keyword", "hybrid")
            n_results: 每個查詢返回的結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            **kwargs: 其他參數傳遞給具體的搜索方法
            
        Returns:
            搜索結果列表的列表
        """
        try:
            if not queries:
                return []
            
            results = []
            
            for query in queries:
                if search_type == "semantic":
                    query_results = self.semantic_search(
                        query=query,
                        n_results=n_results,
                        use_reranker=use_reranker,
                        reranker_name=reranker_name,
                        score_threshold=score_threshold
                    )
                elif search_type == "keyword":
                    query_results = self.keyword_search(
                        keyword=query,
                        n_results=n_results,
                        use_reranker=use_reranker,
                        reranker_name=reranker_name,
                        score_threshold=score_threshold
                    )
                elif search_type == "hybrid":
                    keyword = kwargs.get("keyword")
                    query_results = self.hybrid_search(
                        query=query,
                        keyword=keyword,
                        n_results=n_results,
                        use_reranker=use_reranker,
                        reranker_name=reranker_name,
                        score_threshold=score_threshold,
                        **{k: v for k, v in kwargs.items() if k != "keyword"}
                    )
                else:
                    logger.warning(f"Unknown search type: {search_type}, using semantic search")
                    query_results = self.semantic_search(
                        query=query,
                        n_results=n_results,
                        use_reranker=use_reranker,
                        reranker_name=reranker_name,
                        score_threshold=score_threshold
                    )
                
                results.append(query_results)
            
            logger.debug(f"批量搜索完成: {len(queries)} 個查詢, 搜索類型: {search_type}")
            return results
            
        except Exception as e:
            logger.error(f"批量搜索失敗: {str(e)}")
            return []

    def semantic_search_with_reranking(
        self,
        query: str,
        n_results: int = 5,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        語義搜索並自動使用重排序改善結果質量
        
        這是一個便利方法，自動啟用重排序功能的語義搜索
        
        Args:
            query: 搜索查詢
            n_results: 返回結果數量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            重排序後的搜索結果列表
        """
        return self.semantic_search(
            query=query,
            n_results=n_results,
            use_reranker=True,
            reranker_name=reranker_name,
            score_threshold=score_threshold
        )

    def search_with_fallback(
        self,
        query: str,
        primary_method: str = "semantic",
        fallback_methods: Optional[List[str]] = None,
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0,
        min_results_threshold: int = 1
    ) -> Dict[str, Any]:
        """
        帶回退機制的搜索，如果主要方法結果不足會嘗試其他方法。
        
        Args:
            query: 搜索查詢
            primary_method: 主要搜索方法 ("semantic", "keyword", "hybrid")
            fallback_methods: 回退方法列表
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            min_results_threshold: 最小結果數閾值，低於此數會嘗試回退方法
            
        Returns:
            包含搜索結果和使用方法的字典
        """
        try:
            if not query.strip():
                return {"results": [], "method_used": "none", "fallback_used": False}
            
            if fallback_methods is None:
                fallback_methods = ["hybrid", "keyword"] if primary_method == "semantic" else ["semantic"]
            
            # Try primary method
            results = []
            method_used = primary_method
            fallback_used = False
            
            if primary_method == "semantic":
                results = self.semantic_search(
                    query=query,
                    n_results=n_results,
                    use_reranker=use_reranker,
                    reranker_name=reranker_name,
                    score_threshold=score_threshold
                )
            elif primary_method == "keyword":
                results = self.keyword_search(
                    keyword=query,
                    n_results=n_results,
                    use_reranker=use_reranker,
                    reranker_name=reranker_name,
                    score_threshold=score_threshold
                )
            elif primary_method == "hybrid":
                results = self.hybrid_search(
                    query=query,
                    n_results=n_results,
                    use_reranker=use_reranker,
                    reranker_name=reranker_name,
                    score_threshold=score_threshold
                )
            
            # Check if we need to use fallback methods
            if len(results) < min_results_threshold and fallback_methods:
                logger.debug(f"Primary method '{primary_method}' returned {len(results)} results, trying fallback methods")
                
                for fallback_method in fallback_methods:
                    fallback_results = []
                    
                    if fallback_method == "semantic" and fallback_method != primary_method:
                        fallback_results = self.semantic_search(
                            query=query,
                            n_results=n_results,
                            use_reranker=use_reranker,
                            reranker_name=reranker_name,
                            score_threshold=score_threshold
                        )
                    elif fallback_method == "keyword" and fallback_method != primary_method:
                        fallback_results = self.keyword_search(
                            keyword=query,
                            n_results=n_results,
                            use_reranker=use_reranker,
                            reranker_name=reranker_name,
                            score_threshold=score_threshold
                        )
                    elif fallback_method == "hybrid" and fallback_method != primary_method:
                        fallback_results = self.hybrid_search(
                            query=query,
                            n_results=n_results,
                            use_reranker=use_reranker,
                            reranker_name=reranker_name,
                            score_threshold=score_threshold
                        )
                    
                    if fallback_results and len(fallback_results) >= min_results_threshold:
                        results = fallback_results
                        method_used = fallback_method
                        fallback_used = True
                        logger.debug(f"Using fallback method '{fallback_method}' with {len(results)} results")
                        break
            
            return {
                "results": results,
                "method_used": method_used,
                "fallback_used": fallback_used,
                "total_results": len(results)
            }
            
        except Exception as e:
            logger.error(f"帶回退機制的搜索失敗: {str(e)}")
            return {"results": [], "method_used": "error", "fallback_used": False, "error": str(e)}


# Backward compatibility alias
SearchEngine = ChromaSearchEngine