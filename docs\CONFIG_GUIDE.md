# TongRAG3 配置參數完整指南

> **TongRAG3 v0.2.0** - 智能文檔檢索系統完整配置指南（含重排序和對比功能）

## 📋 目錄

- [核心配置參數](#核心配置參數)
- [重排序配置](#重排序配置)
- [對比功能配置](#對比功能配置)
- [環境變數設置](#環境變數設置)
- [配置文件格式](#配置文件格式)
- [常用配置範例](#常用配置範例)
- [性能調優配置](#性能調優配置)
- [特殊情境配置](#特殊情境配置)

## ⚙️ 核心配置參數

### 基礎搜索配置

```python
# 搜索基本參數
DEFAULT_N_RESULTS = 8              # 默認搜索結果數量
PAGE_SIZE = 5                      # 每頁顯示結果數
CHUNK_SIZE = 500                   # 文檔分塊大小
CHUNK_OVERLAP = 100               # 分塊重疊大小
COLLECTION_NAME = "default"        # 集合名稱

# 搜索質量參數
SIMILARITY_THRESHOLD = 0.5         # 相似度閾值
MIN_CHUNK_LENGTH = 50             # 最小文檔塊長度
MAX_CHUNK_LENGTH = 2000           # 最大文檔塊長度
```

### 進階搜索參數

```python
# 向量搜索配置
EMBEDDING_MODEL = "BAAI/bge-m3"    # 嵌入模型名稱
EMBEDDING_DIMENSION = 1024         # 向量維度
VECTOR_DISTANCE_METRIC = "cosine"  # 距離度量方法

# 混合搜索配置
HYBRID_SEARCH_ENABLED = True       # 啟用混合搜索
SEMANTIC_WEIGHT = 0.7             # 語義搜索權重
KEYWORD_WEIGHT = 0.3              # 關鍵詞搜索權重

# ChromaDB 配置
CHROMA_DATA_PATH = "./chroma_data" # ChromaDB 數據路徑
CHROMA_PERSIST_DIRECTORY = True    # 持久化存儲
CHROMA_CONNECTION_TIMEOUT = 30     # 連接超時時間
```

## 🤖 重排序配置

> TongRAG3 v0.2.0 新增的 BGE 重排序功能配置選項

### 重排序核心參數

```python
# 重排序功能控制
RERANK_ENABLED = True              # 是否啟用重排序功能
RERANK_TOP_K = 15                  # 重排序的候選結果數量
RERANK_THRESHOLD = 0.1             # 重排序分數閾值
RERANK_MODEL = "BAAI/bge-reranker-base"  # 重排序模型名稱

# 重排序性能參數
RERANK_MAX_LENGTH = 512            # 最大輸入長度
RERANK_BATCH_SIZE = 32             # 批處理大小
RERANK_DEVICE = "auto"              # 計算裝置（auto/cpu/cuda）
RERANK_PRECISION = "float16"        # 模型精度（float16/float32）- 優化後默認

# 重排序進階參數
RERANK_ENABLE_CACHING = True       # 啟用結果快取
RERANK_CACHE_SIZE = 1000           # 快取大小
RERANK_MIN_CANDIDATES = 5          # 最少候選數量
RERANK_MAX_CANDIDATES = 50         # 最多候選數量
```

### 重排序環境變數

```bash
# 基礎設置
export RERANK_ENABLED=true
export RERANK_TOP_K=15
export RERANK_THRESHOLD=0.1

# 性能優化
export RERANK_BATCH_SIZE=16
export RERANK_MAX_LENGTH=256
export RERANK_DEVICE=cpu

# 高級設置
export RERANK_ENABLE_CACHING=true
export RERANK_CACHE_SIZE=500
```

### 重排序參數詳解

| 參數 | 預設值 | 推薦範圍 | 說明 |
|------|---------|------------|------|
| `RERANK_ENABLED` | `True` | `True/False` | 是否啟用重排序功能 |
| `RERANK_TOP_K` | `15` | `10-25` | 重排序候選結果數量，較大值可提高品質但消耗更多資源 |
| `RERANK_THRESHOLD` | `0.1` | `0.05-0.2` | 分數閾值，低於此值的結果將被過濾 |
| `RERANK_BATCH_SIZE` | `32` | `8-64` | 批處理大小，影響記憶體使用和處理速度 |
| `RERANK_MAX_LENGTH` | `512` | `256-512` | 最大輸入長度，較短可提高速度 |

### 重排序模型配置

```python
# 支持的重排序模型
SUPPORTED_RERANK_MODELS = {
    "bge-reranker-base": {
        "model_name": "BAAI/bge-reranker-base",
        "max_length": 512,
        "languages": ["zh", "en"],
        "description": "標準 BGE 重排序模型，平衡性能和品質"
    },
    "bge-reranker-large": {
        "model_name": "BAAI/bge-reranker-large",
        "max_length": 512,
        "languages": ["zh", "en"],
        "description": "大型 BGE 重排序模型，更高精度但速度較慢"
    },
    "bce-reranker-base": {
        "model_name": "maidalun1020/bce-reranker-base_v1",
        "max_length": 512,
        "languages": ["zh", "en"],
        "description": "BCE 重排序模型，替代選擇"
    }
}
```

## ⚖️ 對比功能配置

> TongRAG3 v0.2.0 新增的搜索結果對比分析功能

### 對比功能參數

```python
# 對比功能控制
COMPARISON_ENABLED = True          # 是否啟用對比模式
COMPARISON_RESULT_LIMIT = 8        # 對比顯示的結果數量
COMPARISON_METHODS = ["semantic", "keyword", "hybrid"]  # 對比的搜索方法

# 對比分析參數
COMPARISON_INCLUDE_METADATA = True   # 包含元數據資訊
COMPARISON_DETAILED_ANALYSIS = True  # 啟用詳細分析
COMPARISON_EXPORT_FORMAT = "json"   # 導出格式（json/csv）

# 對比顯示參數
COMPARISON_PAGE_SIZE = 3            # 對比結果的分頁大小
COMPARISON_MAX_PREVIEW_LENGTH = 100 # 結果預覽最大長度
COMPARISON_SHOW_RANKING_CHANGES = True  # 顯示排名變化

# 批量對比參數
BATCH_COMPARISON_MAX_QUERIES = 20   # 批量對比最大查詢數
BATCH_COMPARISON_TIMEOUT = 300      # 批量處理逾時時間（秒）
```

### 對比環境變數

```bash
# 基礎設置
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=10
export COMPARISON_PAGE_SIZE=5

# 功能控制
export COMPARISON_DETAILED_ANALYSIS=true
export COMPARISON_EXPORT_FORMAT=json
export COMPARISON_INCLUDE_METADATA=false

# 性能設置
export BATCH_COMPARISON_MAX_QUERIES=15
export BATCH_COMPARISON_TIMEOUT=180
```

### 對比結果配置

```python
# 對比結果顯示配置
COMPARISON_DISPLAY_CONFIG = {
    # 選項控制
    "show_scores": True,              # 顯示相關性分數
    "show_sources": True,             # 顯示來源檔案
    "show_preview": True,             # 顯示內容預覽
    "show_ranking_changes": True,     # 顯示排名變化
    "show_statistics": True,          # 顯示統計資訊
    
    # 格式設置
    "score_precision": 2,             # 分數小數位數
    "preview_length": 80,             # 預覽文字長度
    "source_max_length": 30,          # 來源檔名最大長度
    
    # 視覺化設置
    "use_colors": True,               # 使用彩色顯示
    "ranking_change_symbols": True,   # 使用符號標示變化
    "highlight_improvements": True    # 高亮顯示改進項目
}
```

## 🔧 系統配置

```python
# 系統資源配置
MAX_WORKERS = 4                    # 最大工作線程數
MAX_MEMORY_MB = 4096              # 最大記憶體使用量（MB）
ENABLE_GPU = False                # 是否啟用 GPU 加速
THREAD_POOL_SIZE = 8              # 線程池大小

# 快取配置
ENABLE_RESULT_CACHE = True        # 啟用結果快取
CACHE_TTL = 3600                  # 快取存活時間（秒）
MAX_CACHE_SIZE = "500MB"          # 最大快取大小
CACHE_CLEANUP_INTERVAL = 1800     # 快取清理間隔（秒）

# 日誌配置
LOG_LEVEL = "INFO"                # 日誌級別（DEBUG/INFO/WARNING/ERROR）
LOG_TO_FILE = True                # 是否寫入文件
LOG_FILE_PATH = "logs/tongrag3.log"  # 日誌文件路徑
LOG_MAX_SIZE = "50MB"             # 日誌文件最大大小
LOG_BACKUP_COUNT = 5              # 保留日誌文件數量
```

## 🌍 環境變數設置

### 所有可用環境變數總覽

```bash
# ===================
# 基礎搜索配置
# ===================
export DEFAULT_N_RESULTS=8
export PAGE_SIZE=5
export CHUNK_SIZE=500
export COLLECTION_NAME="tongrag_docs"

# ===================
# 重排序功能配置
# ===================
export RERANK_ENABLED=true
export RERANK_TOP_K=15
export RERANK_THRESHOLD=0.1
export RERANK_MODEL="BAAI/bge-reranker-base"
export RERANK_BATCH_SIZE=32
export RERANK_MAX_LENGTH=512
export RERANK_DEVICE="auto"
export RERANK_ENABLE_CACHING=true
export RERANK_CACHE_SIZE=1000

# ===================
# 對比功能配置  
# ===================
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=8
export COMPARISON_PAGE_SIZE=3
export COMPARISON_DETAILED_ANALYSIS=true
export COMPARISON_EXPORT_FORMAT="json"
export BATCH_COMPARISON_MAX_QUERIES=20

# ===================
# 性能和資源配置
# ===================
export MAX_WORKERS=4
export MEMORY_LIMIT="4GB"
export ENABLE_GPU=false
export LOG_LEVEL="INFO"

# 啟動系統
python main.py
```

### 日誌和調試環境變數

```bash
# 日誌級別設置
export LOG_LEVEL="DEBUG"          # DEBUG/INFO/WARNING/ERROR
export ENABLE_FILE_LOGGING=true   # 啟用文件日誌
export LOG_FILE_PATH="logs/tongrag3.log"
export LOG_MAX_SIZE="50MB"         # 日誌文件最大大小
export LOG_BACKUP_COUNT=5          # 保留日誌文件數量

# 詳細調試信息
export DETAILED_LOGGING=true       # 詳細日誌
export DEBUG_RERANKING=true        # 重排序調試信息
export DEBUG_COMPARISON=true       # 對比功能調試
export TIMING_ANALYSIS=true        # 時間分析
export MEMORY_PROFILING=false      # 記憶體分析（潛在性能影響）

# 錯誤處理
export ENABLE_ERROR_RECOVERY=true  # 啟用錯誤恢復
export ERROR_RETRY_ATTEMPTS=3      # 錯誤重試次數
export ERROR_RETRY_DELAY=1.0       # 重試延遲（秒）
```

## 📁 配置文件格式

### 新版本配置文件示例 (v0.2.0)

**config.yaml** 完整示例：

```yaml
# TongRAG3 v0.2.0 配置文件
# 支持重排序和對比功能

# ===================
# 基礎系統配置
# ===================
system:
  version: "0.2.0"
  name: "TongRAG3"
  environment: "production"  # development/testing/production

# ===================
# 搜索基礎配置
# ===================
search:
  default_n_results: 8
  page_size: 5
  chunk_size: 500
  collection_name: "tongrag_docs"
  
  # 搜索方法配置
  methods:
    semantic:
      enabled: true
      weight: 0.7
    keyword:
      enabled: true  
      weight: 0.3
    hybrid:
      enabled: true
      semantic_weight: 0.7
      keyword_weight: 0.3
    regex:
      enabled: true
    metadata:
      enabled: true

# ===================
# 重排序功能配置
# ===================
reranking:
  enabled: true
  
  # 模型配置
  model:
    name: "BAAI/bge-reranker-base"
    device: "auto"  # auto/cpu/cuda
    precision: "float32"  # float16/float32
    max_length: 512
    
  # 處理參數
  processing:
    top_k: 15
    threshold: 0.1
    batch_size: 32
    min_candidates: 5
    max_candidates: 50
    
  # 性能參數
  performance:
    enable_caching: true
    cache_size: 1000
    timeout: 30
    max_workers: 2
    
  # 進階設置
  advanced:
    score_normalization: "sigmoid"
    diversity_penalty: 0.1
    enable_filtering: true
    filter_threshold: 0.05

# ===================
# 對比功能配置
# ===================
comparison:
  enabled: true
  
  # 對比方法
  methods:
    - "semantic"
    - "keyword" 
    - "hybrid"
    
  # 顯示設置
  display:
    result_limit: 8
    page_size: 3
    preview_length: 100
    show_ranking_changes: true
    show_statistics: true
    use_colors: true
    
  # 分析設置
  analysis:
    detailed_analysis: true
    include_metadata: true
    calculate_metrics: true
    overlap_analysis: true
    
  # 導出設置
  export:
    format: "json"  # json/csv/xlsx
    include_full_text: false
    compression: true
    auto_timestamp: true
    
  # 批量處理
  batch:
    max_queries: 20
    timeout: 300
    parallel_processing: true
    progress_display: true

# ===================
# 性能和資源配置
# ===================
performance:
  max_workers: 4
  memory_limit: "4GB"
  enable_gpu: false
  thread_pool_size: 8
  
  # 快取設置
  cache:
    enable_result_cache: true
    cache_ttl: 3600  # 秒
    max_cache_size: "500MB"
    cache_cleanup_interval: 1800
    
# ===================
# 日誌和監控配置
# ===================
logging:
  level: "INFO"  # DEBUG/INFO/WARNING/ERROR
  file_logging: true
  file_path: "logs/tongrag3.log"
  max_file_size: "50MB"
  backup_count: 5
  
  # 詳細設置
  detailed:
    enable_detailed_logging: false
    debug_reranking: false
    debug_comparison: false
    timing_analysis: false
    memory_profiling: false
    
# ===================
# 錯誤處理設置
# ===================
error_handling:
  enable_recovery: true
  retry_attempts: 3
  retry_delay: 1.0
  fallback_mode: true
  graceful_degradation: true

# ===================
# 開發者設置
# ===================
developer:
  debug_mode: false
  profiling: false
  test_mode: false
  mock_models: false
  
# ===================
# 模型下載和管理
# ===================
models:
  auto_download: true
  download_timeout: 300
  model_cache_dir: "~/.cache/tongrag3/models"
  
  # 可用模型配置
  available_models:
    embedding:
      - name: "BAAI/bge-m3"
        description: "多語言嵌入模型"
        size: "2GB"
      - name: "BAAI/bge-large-zh-v1.5"
        description: "中文大型嵌入模型"
        size: "3GB"
        
    reranking:
      - name: "BAAI/bge-reranker-base"
        description: "標準重排序模型"
        size: "1GB"
      - name: "BAAI/bge-reranker-large"
        description: "大型重排序模型"
        size: "2.5GB"
```

### JSON 配置文件簡化版本

```json
{
  "system": {
    "version": "0.2.0",
    "environment": "development"
  },
  "search": {
    "default_n_results": 8,
    "page_size": 5,
    "chunk_size": 500
  },
  "reranking": {
    "enabled": true,
    "model": {
      "name": "BAAI/bge-reranker-base",
      "device": "auto"
    },
    "processing": {
      "top_k": 15,
      "threshold": 0.1,
      "batch_size": 32
    }
  },
  "comparison": {
    "enabled": true,
    "display": {
      "result_limit": 8,
      "page_size": 3
    }
  }
}
```

## 🚀 性能調優配置

### 低配置環境優化

適用於 4GB 以下記憶體的系統：

```bash
# 減少記憶體使用
export RERANK_TOP_K=10
export RERANK_BATCH_SIZE=8
export RERANK_MAX_LENGTH=256
export RERANK_PRECISION="float16"
export RERANK_DEVICE="cpu"

# 簡化對比功能
export COMPARISON_RESULT_LIMIT=5
export COMPARISON_PAGE_SIZE=2
export COMPARISON_DETAILED_ANALYSIS=false
export COMPARISON_INCLUDE_METADATA=false

# 系統優化
export PAGE_SIZE=3
export DEFAULT_N_RESULTS=5
export MAX_WORKERS=2
```

### 高效能環境優化

適用於 8GB 以上記憶體和 GPU 的系統：

```bash
# 提高處理品質
export RERANK_TOP_K=25
export RERANK_BATCH_SIZE=32
export RERANK_MAX_LENGTH=512
export RERANK_THRESHOLD=0.05
export RERANK_DEVICE="cuda"

# 增強對比功能
export COMPARISON_RESULT_LIMIT=15
export COMPARISON_PAGE_SIZE=5
export COMPARISON_DETAILED_ANALYSIS=true
export BATCH_COMPARISON_MAX_QUERIES=50

# 系統優化
export PAGE_SIZE=8
export DEFAULT_N_RESULTS=15
export MAX_WORKERS=8
export ENABLE_GPU=true
```

### 🎯 重排序效能優化 (2024年優化)

基於實際效能測試的最佳實踐配置，可將重排序時間從28秒優化至6秒以內：

```bash
# =============================
# 重排序效能優化配置 (推薦)
# =============================

# 使用輕量級模型 (30-50% 效能提升)
export RERANK_MODEL="BAAI/bge-reranker-base"  # 替代 bge-reranker-v2-m3

# 啟用 FP16 半精度運算 (15-30% 效能提升)
export RERANK_PRECISION="float16"             # 替代 float32

# 優化批次處理大小 (10-20% 效能提升)
export RERANK_BATCH_SIZE=32                   # 替代預設 16

# 其他效能相關設定
export RERANK_MAX_LENGTH=512                  # 保持標準長度
export RERANK_DEVICE="auto"                   # 自動選擇最佳設備
export RERANK_ENABLE_CACHING=true            # 啟用結果快取

# 預期效能提升
# - 總體提升: ~78%
# - 基準時間: 28.2秒 (100文檔)
# - 優化時間: ~6.4秒 (100文檔)
# - 目標時間: <2秒 (需進一步優化硬體或模型)
```

#### 🔧 效能調優說明

| 優化項目 | 原始設定 | 優化設定 | 預期提升 | 說明 |
|----------|----------|----------|----------|------|
| 模型選擇 | `bge-reranker-v2-m3` | `bge-reranker-base` | 30-50% | 輕量級模型，較小但保持良好精度 |
| 精度設定 | `float32` | `float16` | 15-30% | 半精度運算，記憶體和計算效率提升 |
| 批次大小 | `16` | `32` | 10-20% | 更有效的GPU/CPU批次處理 |

#### 📊 效能測試結果

根據實際測試（100文檔重排序）：
- **基準配置**: 28.231秒
- **優化配置**: 預估6.352秒
- **效能提升**: 4.4倍加速
- **記憶體使用**: 降低約20-30%

```

### 產生環境優化

適用於產生部署和高併發場景：

```bash
# 平衡性能和資源使用
export RERANK_TOP_K=15
export RERANK_BATCH_SIZE=32
export RERANK_ENABLE_CACHING=true
export RERANK_CACHE_SIZE=2000

# 穩定性配置
export RERANK_TIMEOUT=30
export BATCH_COMPARISON_TIMEOUT=600
export ENABLE_ERROR_RECOVERY=true

# 日誌和監控
export LOG_LEVEL="WARNING"
export ENABLE_METRICS=true
export METRICS_EXPORT_INTERVAL=300
```

## 📄 常用配置範例

### 在家使用者配置

適用於個人學習和研究使用：

```bash
#!/bin/bash
# 在家使用者配置

# 基礎設置
export PAGE_SIZE=5
export DEFAULT_N_RESULTS=8

# 重排序設置（中等品質）
export RERANK_ENABLED=true
export RERANK_TOP_K=12
export RERANK_THRESHOLD=0.1
export RERANK_DEVICE="auto"

# 對比功能（簡化版）
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=6
export COMPARISON_PAGE_SIZE=3

# 啟動系統
echo "啟動 TongRAG3 在家版本..."
python main.py
```

### 學術研究配置

適用於學術研究和深度分析：

```bash
#!/bin/bash
# 學術研究配置

# 高品質搜索設置
export DEFAULT_N_RESULTS=15
export PAGE_SIZE=8

# 精確重排序設置
export RERANK_ENABLED=true
export RERANK_TOP_K=25
export RERANK_THRESHOLD=0.05
export RERANK_BATCH_SIZE=64
export RERANK_ENABLE_CACHING=true

# 完整對比分析
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=15
export COMPARISON_DETAILED_ANALYSIS=true
export COMPARISON_INCLUDE_METADATA=true
export BATCH_COMPARISON_MAX_QUERIES=50

# 詳細日誌
export LOG_LEVEL="DEBUG"
export DETAILED_LOGGING=true
export DEBUG_RERANKING=true
export TIMING_ANALYSIS=true

echo "啟動 TongRAG3 學術版本..."
python main.py
```

### 企業環境配置

適用於企業內部部署和團隊使用：

```bash
#!/bin/bash
# 企業環境配置

# 平衡性能設置
export PAGE_SIZE=6
export DEFAULT_N_RESULTS=10
export MAX_WORKERS=6

# 穩定重排序設置
export RERANK_ENABLED=true
export RERANK_TOP_K=15
export RERANK_THRESHOLD=0.1
export RERANK_TIMEOUT=20
export RERANK_ENABLE_CACHING=true
export RERANK_CACHE_SIZE=2000

# 專業對比功能
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=10
export COMPARISON_EXPORT_FORMAT="json"
export BATCH_COMPARISON_MAX_QUERIES=30

# 穩定性設置
export ENABLE_ERROR_RECOVERY=true
export ERROR_RETRY_ATTEMPTS=3
export LOG_LEVEL="INFO"

# 資源控制
export MEMORY_LIMIT="8GB"
export ENABLE_GPU=false

echo "啟動 TongRAG3 企業版本..."
python main.py
```

### 測試和開發配置

適用於功能開發和系統測試：

```bash
#!/bin/bash
# 開發測試配置

# 快速迭代設置
export PAGE_SIZE=3
export DEFAULT_N_RESULTS=5

# 調試重排序設置
export RERANK_ENABLED=true
export RERANK_TOP_K=8
export RERANK_BATCH_SIZE=8
export RERANK_DEVICE="cpu"
export DEBUG_RERANKING=true

# 測試對比功能
export COMPARISON_ENABLED=true
export COMPARISON_RESULT_LIMIT=5
export COMPARISON_PAGE_SIZE=2
export DEBUG_COMPARISON=true

# 詳細調試信息
export LOG_LEVEL="DEBUG"
export DETAILED_LOGGING=true
export TIMING_ANALYSIS=true
export ENABLE_ERROR_RECOVERY=false  # 於測試中快速失敗

# 測試模式
export TEST_MODE=true
export MOCK_SLOW_OPERATIONS=false

echo "啟動 TongRAG3 開發測試版本..."
python main.py
```

### CI/CD 環境配置

適用於自動化測試和持續集成：

```bash
#!/bin/bash
# CI/CD 環境配置

# 最小資源使用
export PAGE_SIZE=2
export DEFAULT_N_RESULTS=3
export MAX_WORKERS=1

# 快速重排序
export RERANK_ENABLED=true
export RERANK_TOP_K=5
export RERANK_BATCH_SIZE=4
export RERANK_THRESHOLD=0.2
export RERANK_DEVICE="cpu"
export RERANK_TIMEOUT=10

# 簡化對比
export COMPARISON_ENABLED=false  # CI 中關閉對比
export BATCH_COMPARISON_MAX_QUERIES=3

# 簡化日誌
export LOG_LEVEL="ERROR"
export DETAILED_LOGGING=false
export ENABLE_FILE_LOGGING=false

# 严格錯誤處理
export ENABLE_ERROR_RECOVERY=false
export FAIL_FAST=true

echo "Running TongRAG3 in CI/CD mode..."
python main.py --test-mode
```

## 🔍 特殊情境配置

### 大文檔庫配置

適用於處理大量文檔（>10GB）的情況：

```bash
# 優化大數據處理
export CHUNK_SIZE=800
export CHUNK_OVERLAP=150
export DEFAULT_N_RESULTS=20

# 強化重排序
export RERANK_TOP_K=30
export RERANK_ENABLE_CACHING=true
export RERANK_CACHE_SIZE=5000

# 批量處理優化
export MAX_WORKERS=8
export THREAD_POOL_SIZE=16
export BATCH_COMPARISON_MAX_QUERIES=100
```

### 實時查詢優化

適用於需要快速響應的場景：

```bash
# 速度優先設置
export RERANK_TOP_K=10
export RERANK_BATCH_SIZE=16
export RERANK_MAX_LENGTH=256
export RERANK_TIMEOUT=5

# 簡化功能
export COMPARISON_DETAILED_ANALYSIS=false
export COMPARISON_INCLUDE_METADATA=false
export LOG_LEVEL="WARNING"
```

### 離線環境配置

適用於無網路連接的環境：

```bash
# 禁用在線功能
export AUTO_DOWNLOAD_MODELS=false
export OFFLINE_MODE=true
export CHECK_MODEL_UPDATES=false

# 使用本地快取
export USE_LOCAL_CACHE_ONLY=true
export MODEL_CACHE_DIR="/local/models"
```

---

## 📊 配置驗證和測試

### 配置有效性檢查

```bash
# 檢查配置是否正確載入
python main.py --config-check

# 檢查特定功能配置
python -c "
from src.config import get_config, validate_config
config = get_config()
print('重排序啟用:', config.get('RERANK_ENABLED', False))
print('對比功能:', config.get('COMPARISON_ENABLED', False))
print('配置有效:', validate_config())
"
```

### 性能基準測試

```bash
# 基準測試腳本
export PERFORMANCE_TEST=true
export TEST_QUERIES="Python,Django,機器學習,數據庫優化"
python main.py --benchmark
```

### 配置優化建議

基於不同硬體配置的優化建議：

| 系統配置 | CPU | 記憶體 | 推薦設置 |
|------------|-----|--------|----------|
| 低配置 | 2核心 | 2-4GB | `RERANK_TOP_K=8, BATCH_SIZE=4` |
| 中等配置 | 4核心 | 4-8GB | `RERANK_TOP_K=15, BATCH_SIZE=16` |
| 高配置 | 8核心+ | 8GB+ | `RERANK_TOP_K=25, BATCH_SIZE=32` |
| GPU加速 | 任意 | 8GB+ | `RERANK_DEVICE=cuda, BATCH_SIZE=64` |

---

## 📞 技術支持

- **配置問題**: 查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- **性能優化**: 參考 [RERANKING_GUIDE.md](RERANKING_GUIDE.md)
- **對比功能**: 參考 [COMPARISON_GUIDE.md](COMPARISON_GUIDE.md)
- **架構理解**: 參考 [ARCHITECTURE.md](ARCHITECTURE.md)

**最後更新時間**: 2025-08-29  
**文檔版本**: v3.0  
**適用系統版本**: TongRAG3 v0.2.0+

---

*完善的配置，打造完美的搜索體驗！*