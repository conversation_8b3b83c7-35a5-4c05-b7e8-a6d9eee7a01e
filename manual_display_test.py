#!/usr/bin/env python3
"""
Manual Display Test for TongRAG3
================================

This script provides a quick way to test the visual display formatting
to confirm that all results show with yellow titles, 🔗 markers, 
and overlap sections as expected.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_mock_results():
    """Create mock search results for testing"""
    return [
        {
            'id': 'test_result_1',
            'text': 'This is the first test document with some content for testing the display formatting.',
            'distance': 0.1,
            'source': 'test1.txt',
            'metadata': {
                'file_name': 'test1.txt',
                'chunk_id': 'chunk_1'
            }
        },
        {
            'id': 'test_result_2', 
            'text': 'This is the second test document which should also be formatted consistently.',
            'distance': 0.15,
            'source': 'test2.txt', 
            'metadata': {
                'file_name': 'test2.txt',
                'chunk_id': 'chunk_2'
            }
        },
        {
            'id': 'test_result_3',
            'text': 'And this is the third test document to verify the formatting across multiple results.',
            'distance': 0.2,
            'source': 'test3.txt',
            'metadata': {
                'file_name': 'test3.txt', 
                'chunk_id': 'chunk_3'
            }
        }
    ]

def test_display_formatting():
    """Test the display formatting with mock results"""
    try:
        from src.utils import format_results
        from src.ui.ui_search.display import DisplayMixin
        from src.ui.ui_base import BaseUI
        
        print("=" * 60)
        print("Manual Display Test - Visual Verification")
        print("=" * 60)
        print()
        
        # Create mock results and format them
        mock_results = create_mock_results()
        print(f"Created {len(mock_results)} mock results for testing...")
        
        # Format results 
        formatted_results = format_results(mock_results, enable_overlap_detection=True)
        print(f"Formatted {len(formatted_results)} results with overlap detection")
        print()
        
        # Create a display instance for testing
        class TestDisplay(DisplayMixin, BaseUI):
            def __init__(self):
                self.page_size = 10
                
        display = TestDisplay()
        
        print("VISUAL TEST: All results should show:")
        print("- Yellow colored title")
        print("- 🔗 marker in title") 
        print("- 🟨 重疊區 section (even if empty)")
        print("- 🟩 一般區 section")
        print("=" * 60)
        print()
        
        # Display each result individually
        for i, result in enumerate(formatted_results):
            print(f"Result {i+1}:")
            display.display_single_result(result)
            print()
            
            # Verify key properties
            content_type = result.get('content_type', '')
            has_overlap_preview = 'overlap_preview' in result
            has_unique_preview = 'unique_preview' in result
            
            print(f"   ✓ content_type: {content_type}")
            print(f"   ✓ has_overlap_preview: {has_overlap_preview}")
            print(f"   ✓ has_unique_preview: {has_unique_preview}")
            print()
        
        print("=" * 60)
        print("Manual verification complete!")
        print("Please check that all results above show:")
        print("1. Yellow title color")
        print("2. 🔗 marker in title")
        print("3. 🟨 重疊區 section (may be empty)")
        print("4. 🟩 一般區 section with content")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"Error in manual display test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_display_formatting()
    if success:
        print("✅ Manual display test completed successfully")
    else:
        print("❌ Manual display test failed")
        sys.exit(1)