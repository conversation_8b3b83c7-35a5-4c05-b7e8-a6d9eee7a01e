#!/usr/bin/env python3
"""
簡化測試：快速驗證重疊顯示功能
"""
import sys
import os
import logging

# 添加項目路徑
sys.path.append(os.getcwd())

from src.search.engine import ChromaSearchEngine

def print_result_analysis(results, option_name):
    """分析並打印結果"""
    print(f"\n📊 {option_name} - 結果分析:")
    print(f"   總結果數: {len(results)}")
    
    overlap_count = 0
    new_system_count = 0  # overlap_detected
    old_system_count = 0  # overlap
    
    for i, result in enumerate(results):
        content_type = result.get("content_type", "unknown")
        has_overlap = result.get("has_overlap", False)
        overlap_preview = result.get("overlap_preview", "")
        unique_preview = result.get("unique_preview", "")
        
        if has_overlap:
            overlap_count += 1
            
        if content_type == "overlap_detected":
            new_system_count += 1
        elif content_type == "overlap":
            old_system_count += 1
            
        # 顯示前3個結果的詳細信息
        if i < 3:
            print(f"\n   結果 {i+1}:")
            print(f"     - 相關度: {result.get('relevance_score', 0):.1f}%")
            print(f"     - 來源: {result.get('source', 'unknown')}")
            print(f"     - content_type: {content_type}")
            print(f"     - has_overlap: {has_overlap}")
            if has_overlap and content_type == "overlap_detected":
                print(f"     - 🟨 重疊預覽: {overlap_preview[:50]}..." if overlap_preview else "     - 🟨 無重疊預覽")
                print(f"     - 🟩 獨有預覽: {unique_preview[:50]}..." if unique_preview else "     - 🟩 無獨有預覽")
    
    print(f"\n   重疊統計:")
    print(f"     - 有重疊標記: {overlap_count}")
    print(f"     - 新系統 (overlap_detected): {new_system_count}")
    print(f"     - 舊系統 (overlap): {old_system_count}")
    
    # 判斷成功
    success = old_system_count == 0
    status = "✅ 通過" if success else "❌ 失敗"
    print(f"   結果: {status} - {'統一使用新系統' if success else '發現舊系統殘留'}")
    
    return success

def main():
    """主測試函數"""
    print("🚀 TongRAG3 重疊顯示功能快速測試\n")
    
    # 設置日志級別
    logging.basicConfig(level=logging.WARNING)
    
    # 初始化搜索引擎
    print("初始化搜索引擎...")
    engine = ChromaSearchEngine()
    
    # 測試選項
    test_cases = [
        ("1. 語義搜索", lambda: engine.semantic_search("MOS 規定", n_results=5)),
        ("2. 關鍵詞搜索", lambda: engine.keyword_search("MOS", n_results=5)),
        ("3. 正則搜索", lambda: engine.regex_search("MOS.*規定", n_results=5)),
        ("4. 混合搜索", lambda: engine.hybrid_search("MOS 規定", "MOS", n_results=5)),
        ("5. 元數據搜索", lambda: engine.semantic_search_with_filters("MOS 規定", {"file_name": {"$contains": "部門"}}, n_results=5)),
        ("6. 語義搜索+重排序", lambda: engine.semantic_search_with_reranking("MOS 規定", n_results=5)),
    ]
    
    results_summary = []
    
    for name, search_func in test_cases:
        print(f"\n{'='*60}")
        print(f"測試: {name}")
        print(f"{'='*60}")
        
        try:
            results = search_func()
            if results:
                success = print_result_analysis(results, name)
                results_summary.append((name, success, len(results)))
            else:
                print(f"❌ {name} - 沒有找到結果")
                results_summary.append((name, False, 0))
                
        except Exception as e:
            print(f"❌ {name} - 執行失敗: {str(e)}")
            results_summary.append((name, False, 0))
    
    # 最終報告
    print(f"\n{'='*60}")
    print("🎯 最終測試報告")
    print(f"{'='*60}")
    
    passed = 0
    for name, success, count in results_summary:
        status = "✅" if success else "❌"
        print(f"{status} {name} - {count} 個結果")
        if success:
            passed += 1
    
    print(f"\n📊 總體結果: {passed}/{len(results_summary)} 個測試通過")
    
    if passed == len(results_summary):
        print("🎉 所有測試通過！重疊檢測系統統一化成功！")
    else:
        print("⚠️  部分測試失敗，需要進一步檢查")
        
    print("\n✨ 重疊顯示效果驗證完成")

if __name__ == "__main__":
    main()