#!/usr/bin/env python3
"""
TongRAG3 Validation Test Script
===============================

This script validates that all has_overlap conditional logic has been removed
and that the system displays all results with consistent formatting.

Test Points:
1. Code verification - no has_overlap conditionals in display.py
2. System functionality - search works with both "test" and "msec cp" queries
3. Format consistency - all results show yellow color, 🔗 marker, overlap sections
4. Debug logging preservation - ensure debug output is maintained
"""

import sys
import re
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_code_verification():
    """Test 1: Verify no has_overlap conditionals remain in display.py"""
    print("🔍 Test 1: Code Verification - Checking for has_overlap conditionals...")
    
    display_file = Path(__file__).parent / "src/ui/ui_search/display.py"
    
    if not display_file.exists():
        print("❌ FAIL: display.py file not found!")
        return False
    
    content = display_file.read_text(encoding='utf-8')
    
    # Search for has_overlap conditionals
    has_overlap_patterns = [
        r'if\s+has_overlap:',
        r'if\s+has_overlap\s*==',
        r'if\s+result\.get\(["\']has_overlap["\']',
        r'if\s+.*has_overlap.*:',
        r'else:\s*#.*has_overlap',
    ]
    
    found_conditionals = []
    for i, line in enumerate(content.split('\n'), 1):
        for pattern in has_overlap_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                found_conditionals.append(f"Line {i}: {line.strip()}")
    
    if found_conditionals:
        print("❌ FAIL: Found has_overlap conditionals in display.py:")
        for conditional in found_conditionals:
            print(f"   {conditional}")
        return False
    
    print("✅ PASS: No has_overlap conditionals found in display.py")
    
    # Verify that all results use yellow color and 🔗 marker
    required_patterns = [
        r'title_color\s*=\s*["\']yellow["\']',
        r'overlap_marker\s*=\s*["\'].*🔗.*["\']',
        r'_display_overlap_sections\(result\)',
    ]
    
    missing_patterns = []
    for pattern in required_patterns:
        if not re.search(pattern, content):
            missing_patterns.append(pattern)
    
    if missing_patterns:
        print("❌ FAIL: Missing required formatting patterns:")
        for pattern in missing_patterns:
            print(f"   {pattern}")
        return False
    
    print("✅ PASS: Required formatting patterns found")
    return True

def test_display_formatting():
    """Test 2: Test that all results use consistent formatting"""
    print("\n🎨 Test 2: Display Formatting - Testing result display consistency...")
    
    try:
        from src.search.engine import ChromaSearchEngine
        from src.utils import format_results
        
        # Initialize engine
        engine = ChromaSearchEngine()
        
        # Test with "test" query
        print("   Testing with query: 'test'")
        test_results = engine.semantic_search("test", n_results=3)
        
        if test_results:
            formatted_results = format_results(test_results)
            
            # Check that all results have overlap format
            all_have_overlap_format = True
            for result in formatted_results:
                content_type = result.get('content_type', '')
                if content_type != 'overlap_detected':
                    print(f"   ❌ Result {result.get('rank', '?')} has content_type: '{content_type}' (expected: 'overlap_detected')")
                    all_have_overlap_format = False
                
                # Check for overlap sections
                if 'overlap_preview' not in result or 'unique_preview' not in result:
                    print(f"   ❌ Result {result.get('rank', '?')} missing preview sections")
                    all_have_overlap_format = False
            
            if all_have_overlap_format:
                print("   ✅ All results have consistent overlap format")
            else:
                print("   ❌ Some results have inconsistent formatting")
                return False
        
        # Test with "msec cp" query  
        print("   Testing with query: 'msec cp'")
        msec_results = engine.semantic_search("msec cp", n_results=3)
        
        if msec_results:
            formatted_results = format_results(msec_results)
            
            # Check consistency
            for result in formatted_results:
                content_type = result.get('content_type', '')
                if content_type != 'overlap_detected':
                    print(f"   ❌ Result {result.get('rank', '?')} has content_type: '{content_type}' (expected: 'overlap_detected')")
                    return False
                    
        print("✅ PASS: Both queries return consistently formatted results")
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Error testing display formatting: {e}")
        return False

def test_utils_format_results():
    """Test 3: Test that format_results function works correctly"""
    print("\n🔧 Test 3: Utils Function - Testing format_results behavior...")
    
    try:
        from src.utils import format_results
        
        # Create mock results
        mock_results = [
            {
                'id': 'test_1',
                'text': 'This is a test document with some content.',
                'distance': 0.1,
                'metadata': {'file_name': 'test.txt', 'chunk_id': 'chunk_1'}
            },
            {
                'id': 'test_2', 
                'text': 'Another test document with different content.',
                'distance': 0.2,
                'metadata': {'file_name': 'test2.txt', 'chunk_id': 'chunk_2'}
            }
        ]
        
        formatted = format_results(mock_results, enable_overlap_detection=True)
        
        # Check all results have required fields
        required_fields = ['content_type', 'overlap_preview', 'unique_preview']
        
        for result in formatted:
            for field in required_fields:
                if field not in result:
                    print(f"   ❌ Result missing field: {field}")
                    return False
                    
            # Check content_type is set correctly
            if result.get('content_type') != 'overlap_detected':
                print(f"   ❌ Result has wrong content_type: {result.get('content_type')}")
                return False
        
        print("✅ PASS: format_results function works correctly")
        return True
        
    except Exception as e:
        print(f"❌ FAIL: Error testing format_results: {e}")
        return False

def test_debug_logging():
    """Test 4: Test that debug logging is preserved"""
    print("\n📝 Test 4: Debug Logging - Checking debug output preservation...")
    
    try:
        from src.utils import format_results
        
        # Redirect stdout to capture debug output
        from io import StringIO
        import contextlib
        
        f = StringIO()
        with contextlib.redirect_stdout(f):
            # Create mock results and format them
            mock_results = [
                {
                    'id': 'debug_test',
                    'text': 'Debug test content',
                    'distance': 0.15,
                    'metadata': {'chunk_id': 'debug_chunk'}
                }
            ]
            
            formatted = format_results(mock_results, enable_overlap_detection=True)
        
        output = f.getvalue()
        
        # Check for debug messages
        debug_patterns = [
            r'\[DEBUG\]',
            r'開始重疊檢測',
            r'重疊檢測完成',
            r'_enhance_result_with_overlap_display',
        ]
        
        found_debug = False
        for pattern in debug_patterns:
            if re.search(pattern, output):
                found_debug = True
                break
        
        if found_debug:
            print("✅ PASS: Debug logging is preserved")
            return True
        else:
            print("⚠️  WARNING: No debug output detected (may be disabled)")
            return True  # Don't fail test, debug might be disabled
            
    except Exception as e:
        print(f"❌ FAIL: Error testing debug logging: {e}")
        return False

def main():
    """Run all validation tests"""
    print("=" * 60)
    print("TongRAG3 has_overlap Conditional Removal Validation")
    print("=" * 60)
    
    tests = [
        test_code_verification,
        test_display_formatting, 
        test_utils_format_results,
        test_debug_logging,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ FAIL: {test.__name__} threw exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SUCCESS: All validation tests passed!")
        print("✅ has_overlap conditional removal is complete and effective")
        return True
    else:
        print(f"⚠️  WARNING: {total - passed} tests failed or had issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)