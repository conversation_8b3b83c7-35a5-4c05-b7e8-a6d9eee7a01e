#!/usr/bin/env python3
"""
手動測試腳本：驗證所有搜索選項的重疊顯示功能
"""
import sys
import os
import logging

# 添加項目路徑
sys.path.append(os.getcwd())

from src.search.engine import ChromaSearchEngine
from src.ui.ui_search.display import DisplayMixin

def test_search_option(option_name, search_func, query_text, **kwargs):
    """
    測試單個搜索選項
    
    Args:
        option_name: 搜索選項名稱
        search_func: 搜索函數
        query_text: 查詢文本
        **kwargs: 其他參數
    """
    print(f"\n{'='*60}")
    print(f"🧪 測試選項：{option_name}")
    print(f"📝 查詢內容：{query_text}")
    print(f"{'='*60}")
    
    try:
        # 執行搜索
        results = search_func(query_text, **kwargs)
        
        if not results:
            print("❌ 沒有找到結果")
            return False
            
        print(f"✅ 找到 {len(results)} 個結果")
        
        # 初始化顯示器
        display = DisplayMixin()
        
        # 分析重疊顯示情況
        overlap_count = 0
        overlap_detected_count = 0
        old_system_count = 0
        
        for i, result in enumerate(results):
            content_type = result.get("content_type", "unknown")
            has_overlap = result.get("has_overlap", False)
            overlap_preview = result.get("overlap_preview", "")
            unique_preview = result.get("unique_preview", "")
            
            if has_overlap:
                overlap_count += 1
                
            if content_type == "overlap_detected":
                overlap_detected_count += 1
                
            if content_type == "overlap":
                old_system_count += 1
                
            print(f"\n🔍 結果 {i+1}:")
            print(f"   - content_type: {content_type}")
            print(f"   - has_overlap: {has_overlap}")
            print(f"   - overlap_preview 存在: {'是' if overlap_preview else '否'}")
            print(f"   - unique_preview 存在: {'是' if unique_preview else '否'}")
            
            # 顯示結果（只顯示前3個）
            if i < 3:
                print(f"   📄 顯示效果：")
                display.display_single_result(result, i+1)
        
        # 統計報告
        print(f"\n📊 統計報告：")
        print(f"   - 總結果數: {len(results)}")
        print(f"   - 有重疊標記: {overlap_count}")
        print(f"   - 新系統格式 (overlap_detected): {overlap_detected_count}")
        print(f"   - 舊系統格式 (overlap): {old_system_count}")
        
        # 判斷是否通過測試
        success = old_system_count == 0  # 沒有舊系統殘留
        if success:
            print("✅ 測試通過：統一使用新的重疊檢測系統")
        else:
            print("❌ 測試失敗：發現舊系統殘留")
            
        return success
        
    except Exception as e:
        print(f"❌ 測試失敗：{str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("🚀 TongRAG3 重疊顯示功能測試")
    print("初始化搜索引擎...")
    
    # 設置日志級別
    logging.basicConfig(level=logging.WARNING)
    
    # 初始化搜索引擎
    engine = ChromaSearchEngine()
    
    # 測試查詢
    test_query = "MOS 規定"
    
    # 測試選項列表
    test_cases = [
        {
            "name": "1. 語義搜索（向量相似度匹配）",
            "func": engine.semantic_search,
            "query": test_query,
            "kwargs": {"n_results": 5}
        },
        {
            "name": "2. 關鍵詞搜索（精確匹配）", 
            "func": engine.keyword_search,
            "query": "MOS",
            "kwargs": {"n_results": 5}
        },
        {
            "name": "3. 正則表達式搜索（模式匹配）",
            "func": engine.regex_search,
            "query": "MOS.*規定",
            "kwargs": {"n_results": 5}
        },
        {
            "name": "4. 混合搜索（語義+關鍵詞）",
            "func": engine.hybrid_search,
            "query": test_query,
            "kwargs": {"keyword": "MOS", "n_results": 5}
        },
        {
            "name": "5. 元數據篩選搜索",
            "func": engine.semantic_search_with_filters,
            "query": test_query,
            "kwargs": {"filters": {"file_name": {"$contains": "部門"}}, "n_results": 5}
        },
        {
            "name": "6. 語義搜索（含重排序）",
            "func": engine.semantic_search_with_reranking,
            "query": test_query,
            "kwargs": {"n_results": 5}
        }
    ]
    
    # 執行測試
    results = []
    for test_case in test_cases:
        success = test_search_option(
            test_case["name"],
            test_case["func"], 
            test_case["query"],
            **test_case["kwargs"]
        )
        results.append((test_case["name"], success))
        
        # 等待用戶確認
        input("\n按 Enter 繼續下一個測試...")
    
    # 最終報告
    print(f"\n{'='*60}")
    print("🎯 最終測試報告")
    print(f"{'='*60}")
    
    passed = 0
    for name, success in results:
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"{status} {name}")
        if success:
            passed += 1
    
    print(f"\n📊 總體結果：{passed}/{len(results)} 個測試通過")
    
    if passed == len(results):
        print("🎉 所有測試通過！重疊檢測系統統一化成功！")
    else:
        print("⚠️  部分測試失敗，需要進一步檢查")

if __name__ == "__main__":
    main()